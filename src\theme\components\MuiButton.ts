import { Components, Theme } from "@mui/material";

const <PERSON><PERSON><PERSON><PERSON><PERSON>: Components<Theme>["MuiButton"] = {
  styleOverrides: {
    root: {
      textTransform: "none" as const,
      boxShadow: "unset",
      whiteSpace: "nowrap" as const,
      fontWeight: 500,
    },
    sizeMedium: {
      fontSize: 14,
      lineHeight: "17px",
      paddingTop: "9.5px",
      paddingBottom: "9.5px",
      height: 36,
      minWidth: 90,
    },
    sizeSmall: {
      fontSize: 12,
      fontWeight: 400,
      lineHeight: "15px",
      paddingTop: "4px",
      paddingBottom: "4px",
    },
    startIcon: {
      "& > *:nth-of-type(1)": {
        fontSize: 16,
      },
    },
    outlined: ({ theme }) => ({
      borderColor: theme.palette.grey["300"],
      color: theme.palette.text.primary,
      ":hover": {
        backgroundColor: theme.palette.grey["200"],
      },
    }),
  },
};

export default MuiButton;
