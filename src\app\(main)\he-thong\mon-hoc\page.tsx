"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { CheckIcon } from "@/components/icons";
import { STATUS_SUBJECT, SUBJECT } from "@/constant/api.const";
import { ISubject } from "@/models/subject.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { updateStatusService } from "@/services/app.service";

const SubjectPage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<ISubject>
      ref={tableRef}
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={SUBJECT}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "searchKey",
          type: "text",
          label: "Tì<PERSON> kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: SUBJECT,
        detailUrl: SUBJECT,
        createUrl: SUBJECT,
        updateUrl: SUBJECT,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default SubjectPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<ISubject>[] => [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên môn học",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "order",
    accessorKey: "order",
    header: "Thứ tự",
    size: 60,
    meta: { align: "center" },
  },
  {
    id: "isSystem",
    header: "Môn hệ thống",
    size: 60,
    meta: { align: "center" },
    cell: ({ row }) =>
      Boolean(row.original.isSystem) && (
        <CheckIcon sx={{ fontSize: 24, color: "primary.main" }} />
      ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_SUBJECT,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên môn học" },
  { id: "order", name: "Thứ tự" },
  { id: "isSystem", name: "Môn hệ thống" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<ISubject>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã môn học",
    size: 12,
    rules: { required: "Mã môn học không được để trống" },
  },
  {
    key: "name",
    type: "text",
    label: "Tên môn học",
    size: 12,
    rules: { required: "Tên môn học không được để trống" },
  },
  {
    key: "order",
    type: "number",
    label: "Thứ tự",
    size: 12,
    defaultValue: 1,
    textFieldNumberProps: { min: 1 },
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
