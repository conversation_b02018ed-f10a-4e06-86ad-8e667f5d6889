{"name": "quan-ly-thiet-bi", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3011", "build": "next build", "start": "next start --port 3011", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.0.2", "@mui/material-nextjs": "^7.0.2", "@mui/x-date-pickers": "6.18.3", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-table": "^8.21.3", "@tinymce/tinymce-react": "^6.1.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "fast-deep-equal": "^3.1.3", "jotai": "^2.12.3", "js-cookie": "^3.0.5", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-infinite-scroll-component": "^6.1.0", "react-multi-date-picker": "^4.5.2", "react-redux": "^9.2.0", "redux-saga": "^1.3.0", "sass": "^1.87.0", "sonner": "^2.0.3", "string-format": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/string-format": "^2.0.3", "eslint": "^9", "eslint-config-next": "15.3.1", "typescript": "^5"}}