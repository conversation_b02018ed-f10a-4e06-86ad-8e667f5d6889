import { AppConstant, PathConstant } from "@/constant";
import { IDomainInfo, ISchool, IUserInfo } from "@/models/app.model";
import { IMenuItemTree, MENU_TYPE } from "@/models/menu.model";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import Cookies from "js-cookie";
import Router from "next/router";
/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetching: boolean;
  error: object | string | null;

  isCollapse: boolean;
  schoolInfo: ISchool | null;

  menuSidebar: IMenuItemTree[];
  expandId: null | number;

  domainInfo: null | IDomainInfo;
  userInfo: null | IUserInfo;
}

const initialState: IInitialState = {
  isFetching: false,
  error: null,

  isCollapse: false,
  schoolInfo: null,

  menuSidebar: [],
  expandId: null,

  domainInfo: null,
  userInfo: null,
};

/* ------------- Selector ------------- */

/* ------------- Reducers ------------- */
const reducers = {
  getDomainInfo: (state: IInitialState) => {
    state.isFetching = true;
    state.error = null;
  },
  getDomainInfoSuccess(
    state: IInitialState,
    action: PayloadAction<IDomainInfo>
  ) {
    state.domainInfo = action.payload;
  },

  getUserInfo: (
    state: IInitialState,
    action: PayloadAction<{ onSuccess?: () => void }>
  ) => {},
  getUserInfoSuccess: (
    state: IInitialState,
    action: PayloadAction<IUserInfo>
  ) => {
    const newUserInfo = action.payload;
    state.userInfo = newUserInfo;
    if (newUserInfo?.schoolInfos.length === 1) {
      state.schoolInfo = newUserInfo.schoolInfos[0];

      // localStorage.setItem(
      //   AppConstant.LOCAL_STORAGE_KEY.ORG_ID,
      //   state.schoolSelected.id.toString()
      // );

      // // Lấy học ky và năm học
      // const schoolYear = newUserInfo.schoolInfos?.[0]?.schoolYearConfig;
      // if (schoolYear) {
      //   const yearLocal = localStorage.getItem(
      //     AppConstant.LOCAL_STORAGE_KEY.SCHOOL_YEAR_ID_SELECTED
      //   );
      //   if (!yearLocal) {
      //     state.schoolYearIdSelected = schoolYear.schoolYearId;
      //     localStorage.setItem(
      //       AppConstant.LOCAL_STORAGE_KEY.SCHOOL_YEAR_ID_SELECTED,
      //       schoolYear.schoolYearId.toString()
      //     );
      //   }
      //   const semesterLocal = localStorage.getItem(
      //     AppConstant.LOCAL_STORAGE_KEY.SEMESTER_SELECTED
      //   );

      //   if (!semesterLocal) {
      //     const semester = dayjs().isBefore(dayjs(schoolYear.startOfSemester2))
      //       ? SEMESTER.semester1
      //       : SEMESTER.semester2;
      //     state.semesterSelected = semester;
      //     localStorage.setItem(
      //       AppConstant.LOCAL_STORAGE_KEY.SEMESTER_SELECTED,
      //       semester.toString()
      //     );
      //   }
      // }
    }
  },

  getMenuSideBar: (state: IInitialState, action: PayloadAction<MENU_TYPE>) => {
    state.isFetching = true;
  },
  getMenuSideBarSuccess: (
    state: IInitialState,
    action: PayloadAction<IMenuItemTree[]>
  ) => {
    state.isFetching = false;
    state.menuSidebar = action.payload;
    state.error = null;
  },
  toggleSideBar: (state: IInitialState) => {
    state.isCollapse = !state.isCollapse;
    state.expandId = null;
  },
  changeExpandId: (
    state: IInitialState,
    action: PayloadAction<number | null>
  ) => {
    state.expandId = action.payload;
  },

  clearToken: (state: IInitialState) => {
    Cookies.remove(AppConstant.ACCESS_TOKEN);
    Cookies.remove(AppConstant.COOKIE_KEY.orgId);
  },
  logout: (state: IInitialState) => {
    Cookies.remove(AppConstant.ACCESS_TOKEN);
    Cookies.remove(AppConstant.COOKIE_KEY.orgId);
    state.domainInfo = null;
    state.userInfo = null;
  },

  // Common
  appFailure: (state: IInitialState, action: PayloadAction<any>) => {
    const error = action.payload ? action.payload : {};
    state.isFetching = false;
    state.error = error;
  },
  appReset: (state: IInitialState) => {
    state.isFetching = false;
    state.error = null;

    state.schoolInfo = null;
  },
};

export const appSlice = createSlice({
  name: "appReducer",
  initialState,
  reducers,
});

const appReducer = appSlice.reducer;

export const appActions = appSlice.actions;

export default appReducer;
