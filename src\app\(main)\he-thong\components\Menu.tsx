"use client";

import React, { memo, useState } from "react";
import {
  Box,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Stack,
} from "@mui/material";
import { mockMenuData } from "./MenuData";
import MenuGroup from "./MenuGroup";
import MenuList from "./MenuList";
import { GridViewIcon, ListViewIcon } from "./icons";
import { ViewMode } from "./enums";

const Menu: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.GROUP);

  const handleViewModeChange = (
    _: React.MouseEvent<HTMLElement>,
    newViewMode: ViewMode
  ) => {
    setViewMode(newViewMode);
  };

  const systemList = useMemo(() => {
    return menuSidebar.find((item) => item.href === PathConstant.SYSTEM)
      ?.children;
  }, [menuSidebar]);

  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      <Paper
        elevation={1}
        sx={{
          p: 3,
          borderRadius: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Stack
          direction="row"
          justifyContent="end"
          alignItems="center"
          spacing={2}
        >
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            aria-label="view mode"
            size="small"
            sx={{
              fontSize: 18,
              "& .MuiToggleButton-root": {
                border: "1px solid",
                borderColor: "divider",
                "&.Mui-selected": {
                  backgroundColor: "primary.main",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "primary.dark",
                  },
                },
              },
            }}
          >
            <ToggleButton value={ViewMode.GROUP} aria-label="grid view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <GridViewIcon />
              </Stack>
            </ToggleButton>
            <ToggleButton value={ViewMode.LIST} aria-label="list view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <ListViewIcon />
              </Stack>
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      </Paper>

      {viewMode === ViewMode.GROUP ? (
        <MenuGroup groups={mockMenuData} />
      ) : (
        <MenuList groups={mockMenuData} />
      )}
    </Box>
  );
};

export default memo(Menu);
