"use client";

import React, { memo, useState, useMemo } from "react";
import {
  Box,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Stack,
} from "@mui/material";
import { transformMenuTreeToGroups } from "./MenuData";
import MenuGroup from "./MenuGroup";
import MenuList from "./MenuList";
import { GridViewIcon, ListViewIcon } from "./icons";
import { ViewMode } from "./enums";
import { useAppSelector } from "@/redux/hook";
import { PathConstant } from "@/constant";

const Menu: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.GROUP);
  const menuSidebar = useAppSelector((state) => state.appReducer.menuSidebar);

  const handleViewModeChange = (
    _: React.MouseEvent<HTMLElement>,
    newViewMode: ViewMode
  ) => {
    setViewMode(newViewMode);
  };

  // Get system menu data from sidebar
  const systemMenuData = useMemo(() => {
    // Find the system menu item in the sidebar
    const systemMenuItem = menuSidebar.find(
      (item) => item.href === PathConstant.SYSTEM
    );

    if (
      systemMenuItem &&
      systemMenuItem.children &&
      systemMenuItem.children.length > 0
    ) {
      // Transform the real menu data to our interface format
      const transformedData = transformMenuTreeToGroups(
        systemMenuItem.children
      );
      return transformedData;
    }

    return [];
  }, [menuSidebar]);

  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      <Paper
        elevation={1}
        sx={{
          p: 3,
          borderRadius: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Stack
          direction="row"
          justifyContent="end"
          alignItems="center"
          spacing={2}
        >
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            aria-label="view mode"
            size="small"
            sx={{
              fontSize: 18,
              "& .MuiToggleButton-root": {
                border: "1px solid",
                borderColor: "divider",
                "&.Mui-selected": {
                  backgroundColor: "primary.main",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "primary.dark",
                  },
                },
              },
            }}
          >
            <ToggleButton value={ViewMode.GROUP} aria-label="grid view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <GridViewIcon />
              </Stack>
            </ToggleButton>
            <ToggleButton value={ViewMode.LIST} aria-label="list view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <ListViewIcon />
              </Stack>
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      </Paper>

      {viewMode === ViewMode.GROUP ? (
        <MenuGroup groups={systemMenuData} />
      ) : (
        <MenuList groups={systemMenuData} />
      )}
    </Box>
  );
};

export default memo(Menu);
