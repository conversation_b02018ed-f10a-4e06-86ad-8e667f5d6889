"use client";

import React, { memo, useState } from "react";
import {
  Box,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Stack,
} from "@mui/material";
import { ViewMode, mockMenuData, IMenuItem } from "./MenuData";
import MenuGroup from "./MenuGroup";
import MenuList from "./MenuList";
import { GridViewIcon, ListViewIcon } from "./icons";

interface MenuProps {
  onItemClick?: (item: IMenuItem) => void;
}

const Menu: React.FC<MenuProps> = ({ onItemClick }) => {
  const [viewMode, setViewMode] = useState<ViewMode>("group");

  const handleViewModeChange = (
    _event: React.MouseEvent<HTMLElement>,
    newViewMode: ViewMode | null
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  const handleItemClick = (item: IMenuItem) => {
    console.log("Menu item clicked:", item);
    if (onItemClick) {
      onItemClick(item);
    }
  };

  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      {/* Header with view mode toggle */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          borderRadius: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          spacing={2}
        >
          <Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                color: "text.primary",
                mb: 1,
                fontSize: "1.5rem",
              }}
            >
              Hệ thống
            </Typography>
            {/* <Typography
              variant="body1"
              sx={{
                color: "text.secondary",
                fontSize: "0.95rem",
              }}
            >
              Quản lý cấu hình và thiết lập hệ thống
            </Typography> */}
          </Box>

          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            aria-label="view mode"
            size="small"
            sx={{
              "& .MuiToggleButton-root": {
                border: "1px solid",
                borderColor: "divider",
                "&.Mui-selected": {
                  backgroundColor: "primary.main",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "primary.dark",
                  },
                },
              },
            }}
          >
            <ToggleButton value="group" aria-label="grid view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <GridViewIcon sx={{ fontSize: 18 }} />
              </Stack>
            </ToggleButton>
            <ToggleButton value="list" aria-label="list view">
              <Stack direction="row" alignItems="center" spacing={1}>
                <ListViewIcon sx={{ fontSize: 18 }} />
              </Stack>
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      </Paper>

      {/* Content area */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          backgroundColor:
            viewMode === "group" ? "background.default" : "transparent",
        }}
      >
        {viewMode === "group" ? (
          <MenuGroup groups={mockMenuData} onItemClick={handleItemClick} />
        ) : (
          <MenuList groups={mockMenuData} onItemClick={handleItemClick} />
        )}
      </Box>
    </Box>
  );
};

export default memo(Menu);
