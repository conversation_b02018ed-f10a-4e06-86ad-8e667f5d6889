"use client";

import { TablePageLayout } from "@/components/common";
import StatusCell from "@/components/common/table/cell/StatusCell";
import {
  FormFieldConfig,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { DEVICE, STATUS_DEVICE } from "@/constant/api.const";
import { IDevice } from "@/models/device.model";
import { ColumnDef } from "@tanstack/react-table";
import React, { useRef } from "react";
import { STATUS_TYPE_LIST } from "@/constant/data.const";
import { updateStatusService } from "@/services/app.service";

const SubjectPage = () => {
  const tableRef = useRef<ITableRef>(null);
  const columns = getColumns(tableRef);

  return (
    <TablePageLayout<IDevice>
      ref={tableRef}
      fetchAll
      visibleCol={VISIBLE_COL}
      apiUrl={DEVICE}
      tableProps={{
        columns,
        hasDefaultPagination: true,
      }}
      filterConfig={[
        {
          key: "status",
          type: "select",
          label: "Trạng thái hiển thị",
          size: 2.4,
          options: STATUS_TYPE_LIST,
        },
        {
          key: "searchKey",
          type: "text",
          label: "Tìm kiếm",
          size: 2.4,
        },
      ]}
      actions={["create", "update", "delete"]}
      formConfig={{
        deleteUrl: DEVICE,
        detailUrl: DEVICE,
        createUrl: DEVICE,
        updateUrl: DEVICE,
        createFields: CREATE_CONFIG,
        updateFields: CREATE_CONFIG,
      }}
    />
  );
};

export default SubjectPage;

const getColumns = (
  tableRef: React.RefObject<ITableRef | null>
): ColumnDef<IDevice>[] => [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "name",
    header: "Tên thiết bị",
    accessorKey: "name",
    size: 150,
  },
  {
    id: "note",
    accessorKey: "note",
    header: "Ghi chú",
    size: 200,
    meta: { align: "center" },
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Hiển thị",
    size: 60,
    cell: ({ row }) => (
      <StatusCell
        status={row.original.status}
        onStatusChange={(_, value) => {
          updateStatusService({
            id: row.original.id,
            status: Number(value),
            onSuccess: tableRef?.current?.fetchCurrentData,
            url: STATUS_DEVICE,
          });
        }}
      />
    ),
    meta: { align: "center" },
  },
];

const VISIBLE_COL = [
  { id: "code", name: "Mã" },
  { id: "name", name: "Tên thiết bị" },
  { id: "note", name: "Ghi chú" },
  { id: "status", name: "Hiển thị" },
];

const CREATE_CONFIG: FormFieldConfig<IDevice>[] = [
  {
    key: "code",
    type: "text",
    label: "Mã",
    size: 12,
    textFieldProps: {
      autoFocus: true,
    },
    rules: { required: "Mã thiết bị không được để trống" },
  },
  {
    key: "name",
    type: "text",
    label: "Tên thiết bị",
    size: 12,
    rules: { required: "Tên thiết bị không được để trống" },
  },
  {
    key: "note",
    type: "text",
    label: "Ghi chú",
    textFieldProps: {
      multiline: true,
      minRows: 3,
    },
    size: 12,
  },
  {
    key: "status",
    type: "toggle",
    label: "Trạng thái hiển thị",
    size: 12,
  },
];
