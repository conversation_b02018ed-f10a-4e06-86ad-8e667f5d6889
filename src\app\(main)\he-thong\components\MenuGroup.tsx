"use client";

import React, { memo } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { IMenuGroup, IMenuItem } from "./MenuData";
import MenuCard from "./MenuCard";

type MenuGroupProps = {
  groups: IMenuGroup[];
  onItemClick?: (item: IMenuItem) => void;
};

const MenuGroup: React.FC<MenuGroupProps> = ({ groups, onItemClick }) => {
  return (
    <Box sx={{ p: 3 }}>
      {groups.map((group) => (
        <Box key={group.id} sx={{ mb: 4 }}>
          {/* Group Header */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 700,
                color: "text.primary",
                mb: 1,
                fontSize: "1.25rem",
              }}
            >
              {group.name}
            </Typography>
            {/* {group.description && (
              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: "0.95rem",
                }}
              >
                {group.description}
              </Typography>
            )} */}
          </Box>

          {/* Group Items Grid */}
          <Grid container spacing={3}>
            {group.items.map((item) => (
              <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={item.id}>
                <MenuCard item={item} variant="card" onClick={onItemClick} />
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}
    </Box>
  );
};

export default memo(MenuGroup);
