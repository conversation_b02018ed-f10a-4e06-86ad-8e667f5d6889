"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Too<PERSON><PERSON>,
} from "@mui/material";
import dynamic from "next/dynamic";
import { memo, useMemo, forwardRef, JSX, ReactNode, useState } from "react";
import { useTableContext } from "../..//TableContext";
import ListFilter from "./ListFilter";
import { ActionType, FilterConfig, FilterValueType } from "../../type";
import { useModalActionContext } from "../../ModalActionContext";
import {
  ClearFilterIcon,
  FilterActionsIcon,
  ReloadIcon,
} from "@/components/icons";
import { VisibilityState } from "@tanstack/react-table";
import { IColumnVisible } from "./ColumnToggleButton/useColumnToggle";
import IconButtonCustom from "./IconButtonCustom";

const AdvancedFilter = dynamic(() => import("./AdvancedFilter"), {
  ssr: false,
});

const ColumnToggleButton = dynamic(() => import("./ColumnToggleButton"), {
  ssr: false,
});

const HeaderFilter = <T,>(
  props: HeaderFilterProps<T>,
  ref: React.Ref<HTMLDivElement>
) => {
  const {
    actions,
    customActions,
    visibleCol,
    setColumnVisibility,
    filterCustom,
  } = props;
  const { filter, handleClearFilter, fetchCurrentData, handleChangeFilter } =
    useTableContext<T>();

  const [isOpen, setIsOpen] = useState(true);

  const { openCreateModal } = useModalActionContext<T>();

  const hasAdvanced = useMemo(
    () => filter?.some((item) => Boolean(item.isAdvanced)),
    [filter]
  );

  const filterShow = useMemo(
    () => filter?.filter((item) => !item.isAdvanced && item.type) || [],
    [filter]
  );

  return (
    <Box ref={ref}>
      <Stack
        direction="row"
        py={1}
        px={3}
        sx={{
          borderTop: "1px solid",
          borderColor: "border.main",
        }}
      >
        <Stack gap={1} direction="row">
          {hasAdvanced && <AdvancedFilter />}

          {Boolean(filterShow.length) && (
            <>
              <Tooltip title={isOpen ? "Ẩn bộ lọc" : "Hiện bộ lọc"} arrow>
                <IconButtonCustom onClick={() => setIsOpen(!isOpen)}>
                  <FilterActionsIcon />
                </IconButtonCustom>
              </Tooltip>
              <Tooltip title="Xóa bộ lọc" arrow>
                <IconButtonCustom onClick={handleClearFilter}>
                  <ClearFilterIcon />
                </IconButtonCustom>
              </Tooltip>
            </>
          )}
        </Stack>
        <Stack
          direction="row"
          gap={1}
          justifyContent="flex-end"
          flex={1}
          sx={{ minWidth: 0 }}
        >
          {visibleCol && (
            <ColumnToggleButton
              idKey="table"
              columnVisibleDefault={{}}
              columns={visibleCol}
              onChangeColumnVisibility={setColumnVisibility}
            />
          )}
          <Tooltip arrow title="Tải lại dữ liệu">
            <IconButtonCustom onClick={fetchCurrentData}>
              <ReloadIcon />
            </IconButtonCustom>
          </Tooltip>
          {customActions}
          {actions?.includes("create") && (
            <Button variant="contained" onClick={openCreateModal}>
              Thêm mới
            </Button>
          )}
        </Stack>
      </Stack>

      <Divider />
      {Boolean(filterShow.length) && (
        <Collapse in={isOpen}>
          <Grid
            px={3}
            container
            columnSpacing={SPACING_COLUMN_FILTER}
            rowSpacing={SPACING_ROW_FILTER}
            py={1}
          >
            <>
              <ListFilter filter={filterShow} />
            </>
            {filterCustom?.({ filter, onChangeFilter: handleChangeFilter })}
          </Grid>
        </Collapse>
      )}
    </Box>
  );
};

export const SPACING_ROW_FILTER = 2;
export const SPACING_COLUMN_FILTER = 1.5;
export const DEFAULT_FILTER_SIZE = 2.4;

interface FilterCustomProps {
  filter: FilterConfig[] | null;
  onChangeFilter: (key: string) => (value: FilterValueType) => void;
}

export type HeaderFilterProps<T> = {
  actions?: ActionType[];
  /** Custom các nút header */
  customActions?: ReactNode;

  /** Cấu hình ẩn hiện cột */
  visibleCol?: Array<IColumnVisible>;
  setColumnVisibility: (state: VisibilityState) => void;

  /** Custom filter */
  filterCustom?: (props: FilterCustomProps) => ReactNode;
};

export default memo(forwardRef(HeaderFilter)) as <T>(
  props: HeaderFilterProps<T> & { ref?: React.Ref<HTMLDivElement> }
) => JSX.Element;
