"use client";

import AppModal from "@/components/common/modal/AppModal";
import React, { memo, JSX } from "react";
import { FormConfig } from "../../type";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import FromActionsModal from "./FromActionsModal";
import useActionsData from "./hooks/useActionsData";
import useDefaultFormValues from "./hooks/useDefaultFormValues";

const CreateModal = <T,>({
  isOpen,
  onClose,
  formConfig,
}: CreateModalProps<T>) => {
  const { handleCreateData } = useActionsData<T>();

  const defaultValues = useDefaultFormValues(formConfig?.createFields);

  const methods = useForm({
    defaultValues,
  });

  const { control, handleSubmit, reset } = methods;

  const handleClose = () => {
    onClose();
    reset(defaultValues);
  };

  const handleSubmitData = (data: Record<string, any>) => {
    handleCreateData(data, formConfig, handleClose);
  };

  return (
    <FormProvider {...methods}>
      <AppModal
        onSubmit={handleSubmit(handleSubmitData)}
        isOpen={isOpen}
        onClose={handleClose}
        modalTitleProps={{
          title: "Thêm mới",
        }}
        slotProps={{
          paper: {
            component: "form",
          },
        }}
        modalContentProps={{
          content: (
            <FromActionsModal<any>
              control={control}
              formField={formConfig?.createFields}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button variant="outlined" onClick={handleClose}>
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...formConfig?.modalProps}
      />
    </FormProvider>
  );
};

type CreateModalProps<T> = {
  isOpen: boolean;
  onClose: () => void;
  formConfig?: FormConfig<T>;
};

export default memo(CreateModal) as <T>(
  props: CreateModalProps<T>
) => JSX.Element;
