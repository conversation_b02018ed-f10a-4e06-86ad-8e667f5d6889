import { AppImage } from "@/components/common";
import { ImageConstant } from "@/constant";
import { Box, Stack, Typography } from "@mui/material";
import { memo } from "react";

const LeftSide = () => {
  return (
    <Box
      sx={{
        height: "100%",
        width: 640,
        maxWidth: 640,
        py: 3,
        pl: "60px",
        pr: "40px",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        color: "common.white",
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1.5}>
        <div
          style={{
            width: 60,
            height: 60,
            backgroundColor: "white",
            padding: 9,
            borderRadius: "8px",
          }}
        >
          <AppImage
            alt="logo"
            src={ImageConstant.LogoImage}
            width={42}
            height={42}
          />
        </div>
        <Typography fontSize={15} fontWeight={800}>
          eNetDevice
        </Typography>
      </Stack>
      <Typography
        fontSize={48}
        lineHeight={"71px"}
        fontWeight={700}
        letterSpacing="-1px"
        marginTop={"67px"}
      >
        Hệ thống{" "}
        <Typography
          component="span"
          fontSize={48}
          lineHeight={"71px"}
          fontWeight={700}
          color={"#32C36C"}
        >
          Quản lý thiết bị
        </Typography>{" "}
        trường học
      </Typography>
      <Typography fontSize={17} lineHeight={"36px"} marginTop={2.5}>
        Quản lý các tài liệu, bài giảng, hoạt động, hình ảnh của trường thông
        qua các tài liệu dạng Video, hình ảnh, audio, e-Learning. Quản lý mượn
        sách từ hệ thống quản lý thư viện truyền thống
      </Typography>
      <AppImage
        alt="ảnh thư viện"
        boxProps={{
          mt: "44px",
        }}
        src={ImageConstant.LoginSlide1Image}
        width={363}
        height={325}
      />
    </Box>
  );
};

export default memo(LeftSide);
