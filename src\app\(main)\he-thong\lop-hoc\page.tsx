import { TablePageLayout } from "@/components/common";
import { FormFieldConfig } from "@/components/common/TablePageLayout/type";
import { CLASS, GET_GRADE } from "@/constant/api.const";
import { IClass } from "@/models/class.model";
import { ColumnDef } from "@tanstack/react-table";
import React from "react";

const page = () => {
  return (
    <>
      <TablePageLayout<IClass>
        fetchAll
        visibleCol={VISIBLE_COL}
        apiUrl={CLASS}
        tableProps={{
          columns: COLUMNS,
          hasDefaultPagination: true,
        }}
        filterConfig={[
          {
            key: "gradeCode",
            type: "select",
            label: "Khối",
            size: 2.4,
            apiListUrl: GET_GRADE,
            selectedKey: "code",
          },
        ]}
        actions={["create", "update", "delete"]}
        formConfig={{
          deleteUrl: CLASS,
          detailUrl: CLASS,
          createUrl: CLASS,
          updateUrl: CLASS,
          createFields: CREATE_CONFIG,
          updateFields: CREATE_CONFIG,
        }}
      />
    </>
  );
};

export default page;

const COLUMNS: ColumnDef<IClass>[] = [
  {
    id: "code",
    header: "Mã",
    accessorKey: "code",
    size: 60,
  },
  {
    id: "gradeName",
    header: "Khối",
    accessorKey: "gradeName",
    size: 60,
  },
  {
    id: "name",
    header: "Tên lớp",
    accessorKey: "name",
    size: 200,
  },
  {
    id: "order",
    accessorKey: "order",
    header: "Thứ tự",
    size: 60,
    meta: {
      align: "center",
    },
  },
];

const VISIBLE_COL = [
  {
    id: "code",
    name: "Mã",
  },
  {
    id: "gradeName",
    name: "Khối",
  },
  {
    id: "name",
    name: "Tên lớp",
  },
  {
    id: "order",
    name: "Thứ tự",
  },
];

const CREATE_CONFIG: FormFieldConfig<IClass>[] = [
  {
    key: "gradeCode",
    type: "select",
    label: "Khối",
    size: 12,
    apiListUrl: GET_GRADE,
    rules: {
      required: "Khối không được để trống",
    },
    selectConfig: {
      valueKey: "code",
    },
  },
  {
    key: "code",
    type: "text",
    label: "Mã lớp",
    size: 12,
    rules: {
      required: "Mã lớp không được để trống",
    },
  },
  {
    key: "name",
    type: "text",
    label: "Tên lớp",
    size: 12,
    rules: {
      required: "Tên lớp không được để trống",
    },
  },
  {
    key: "order",
    type: "number",
    label: "Thứ tự",
    size: 12,
    defaultValue: 1,
    textFieldNumberProps: {
      min: 1,
    },
  },
];
