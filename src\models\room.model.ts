import { DataConstant } from "@/constant";
import { ISubject } from "./subject.model";

export interface IRoom {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: Date;
  updatedAt: Date;
  name: string;
  classificationType: number | null;
  isFunctionalClassroom: number;
  isInternetConnection: number | null;
  functionalClassroomTypeId: number;
  area: number | null;
  status: DataConstant.STATUS_TYPE;
  subjects?: ISubject[];
  teachers?: any[];
  teacherName: string;
  subjectName: string;
}

export interface IRoomAction extends IRoom {
  subjectIds?: number[];
  teacherIds?: number[];
}

export enum CLASSIFICATION_TYPE {
  none = 0, // Chưa đạt chuẩn
  standard = 1, // Đạt chuẩn
}

export const CLASSIFICATION_LIST = [
  {
    id: CLASSIFICATION_TYPE.none,
    label: "Chưa đạt chuẩn",
  },
  {
    id: CLASSIFICATION_TYPE.standard,
    label: "Đạt chuẩn",
  },
];
