import http from "@/api";
import { ApiConstant, AppConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { toggleAppProgress } from "@/utils/common.utils";
import { toast } from "sonner";
import dayjs from "dayjs";
import { DATE_TIME_YYYYescape, YEAR_FORMAT } from "@/constant/app.const";
import {
  FormConfig,
  FormFieldConfig,
} from "@/components/common/TablePageLayout/type";
import { useTableContext } from "@/components/common/TablePageLayout/TableContext";

const useActionsData = <T,>() => {
  const { setPagination, cleanDataFormFiled } = useTableContext();
  const handleCreateData = async (
    data: Record<string, any>,
    formConfig?: FormConfig<T>,
    onSuccess?: () => void
  ) => {
    toggleAppProgress(true);
    try {
      let newData = { ...data };

      if (cleanDataFormFiled) {
        newData = cleanDataFormFiled(newData);
      }

      const response: DataResponseModel<unknown> = await http.post(
        formConfig?.createUrl || "",
        cleanData(newData, formConfig?.createFields)
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        setPagination({ ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE });
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Dữ liệu đã được thêm mới thành công.",
        });
        return response.data;
      }

      throw response;
    } catch (error: any) {
      const description =
        error?.errors && typeof error.errors === "object"
          ? Object.values(error.errors).flat().join(", ")
          : error?.message || "Đã xảy ra lỗi trong quá trình thêm mới dữ liệu.";

      toast.error("Thất bại!", {
        description,
      });
      return false;
    } finally {
      toggleAppProgress(false);
    }
  };

  const handleEditData = async (
    data: Record<string, any>,
    formConfig?: FormConfig<T>,
    onSuccess?: () => void
  ) => {
    toggleAppProgress(true);
    try {
      if (!data?.id || !formConfig?.updateUrl)
        throw Error(`id | ! updateUrl undefined`);
      let newData = { ...data };

      if (cleanDataFormFiled) {
        newData = cleanDataFormFiled(newData);
      }

      const response: DataResponseModel<unknown> = await http.put(
        formConfig?.updateUrl + "/" + newData?.id || "",
        cleanData(newData, formConfig?.createFields)
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        setPagination({ ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE });
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Dữ liệu đã được cập nhật thành công.",
        });
        return response.data;
      }

      throw response;
    } catch (error: any) {
      const description =
        error?.errors && typeof error.errors === "object"
          ? Object.values(error.errors).flat().join(", ")
          : error?.message || "Đã xảy ra lỗi trong quá trình cập nhật dữ liệu.";

      toast.error("Thất bại!", {
        description,
      });
      return false;
    } finally {
      toggleAppProgress(false);
    }
  };

  const handleDeleteData = async (
    id?: string | number,
    url?: string,
    onSuccess?: () => void
  ) => {
    toggleAppProgress(true);
    try {
      if (!id || !url) throw Error(`id | url undefined`);

      const response: DataResponseModel<unknown> = await http.delete(
        url + "/" + id || ""
      );

      if (response.code === ApiConstant.ERROR_CODE_OK) {
        setPagination({ ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE });
        onSuccess?.();
        toast.success("Thành công!", {
          description: "Xóa bản ghi thành công.",
        });
        return response.data;
      }

      throw response;
    } catch (error: any) {
      const description =
        error?.errors && typeof error.errors === "object"
          ? Object.values(error.errors).flat().join(", ")
          : error?.message || "Đã xảy ra lỗi trong quá trình xóa mới dữ liệu.";

      toast.error("Thất bại!", {
        description,
      });
      return false;
    } finally {
      toggleAppProgress(false);
    }
  };

  return { handleCreateData, handleEditData, handleDeleteData };
};

export default useActionsData;

const cleanData = (
  data: Record<string, any>,
  formConfig?: FormFieldConfig<any>[]
) => {
  const newData = { ...data };

  if (Object.keys(newData).length) {
    for (const key in newData) {
      const type = formConfig?.find((item) => item.key === key)?.type;

      const value = newData[key];

      // Check boolean
      if (typeof value === "boolean" && type === "toggle") {
        newData[key] = Number(value);
      }
      // check số
      else if (
        typeof value === "string" &&
        !isNaN(Number(value)) &&
        !(value.length > 1 && value.startsWith("0")) &&
        type === "number"
      ) {
        newData[key] = Number(value);
      }
      // Check date
      else if (
        value &&
        typeof value === "string" &&
        dayjs(value).isValid() &&
        type === "date"
      ) {
        const date = dayjs(value);

        const isYear = Boolean(
          formConfig?.find(
            (item) =>
              item.key === key &&
              item.type === "date" &&
              item?.dataPickerProps?.views?.length === 1 &&
              item?.dataPickerProps?.views?.includes("year")
          )
        );

        if (isYear) {
          newData[key] = Number(date.format(YEAR_FORMAT));
        } else {
          newData[key] = date.format(DATE_TIME_YYYYescape);
        }
      }
    }
  }

  return newData;
};
