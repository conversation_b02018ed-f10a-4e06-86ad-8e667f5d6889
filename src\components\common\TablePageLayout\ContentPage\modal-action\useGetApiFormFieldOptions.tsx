import { useEffect, useState } from "react";
import { FormConfig, FormFieldConfig } from "../../type";
import { IOption } from "@/components/common/AppAutoComplete";
import http from "@/api";
import {
  DataListResponseModel,
  DataResponseModel,
} from "@/models/response.model";
import { convertDataToOptions } from "@/utils/format.utils";

export default function useGetApiFormFieldOptions<R>(
  formField?: FormFieldConfig<R>[]
) {
  const [optionsObj, setOptionsObj] = useState<Record<string, IOption[]>>({});

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const itemsWithApi = formField?.filter((f) => f.apiListUrl) || [];

        const responses = await Promise.all(
          itemsWithApi.map((item) =>
            http.get<DataListResponseModel<any> | DataResponseModel<any>>(
              item.apiListUrl!
            )
          )
        );

        const extractData = (res: any) =>
          Array.isArray(res?.data) ? res.data : res?.data?.data || [];

        const optionsMap: Record<string, IOption[]> = {};

        itemsWithApi.forEach((item, idx) => {
          optionsMap[item.key] = convertDataToOptions(
            extractData(responses[idx])
          );
        });

        formField?.forEach((item) => {
          if (!item.apiListUrl) {
            optionsMap[item.key] = item?.selectConfig?.options || [];
          }
        });

        setOptionsObj(optionsMap);
      } catch (err) {
        console.error("Failed to fetch options:", err);
      }
    };

    fetchOptions();
  }, [formField]);

  return optionsObj;
}
