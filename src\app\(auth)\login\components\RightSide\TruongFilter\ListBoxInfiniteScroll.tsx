"use client";

import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { Box, BoxProps } from "@mui/material";
import { forwardRef } from "react";
import { loginActions, loginSelectors } from "../../../login.slice";
import { getPaginationInfo } from "@/components/common/table/AppTable/helper";
import { AppConstant } from "@/constant";
import { AppInfiniteScroll, IOption } from "@/components/common";

const ListBoxInfiniteScroll = forwardRef(
  (
    {
      children,
      divisionCode,
      doetCode,
      groupUnitCode,
      schoolLevel,
      ...otherProps
    }: ListBoxInfiniteScrollProps,
    ref
  ) => {
    const dispatch = useAppDispatch();
    const totalTruong = useAppSelector(loginSelectors.selectTotalTruong);
    const pagination = useAppSelector(loginSelectors.selectPagination);
    const truongList = useAppSelector(loginSelectors.selectTruongList);

    const handleGetMoreData = () => {
      const { currentPage, totalPage } = getPaginationInfo(
        pagination,
        totalTruong
      );
      if (currentPage >= totalPage) return;

      dispatch(
        loginActions.getTruongList({
          doetCode: (doetCode?.code as string) || (doetCode?.code as string),
          divisionCode:
            (divisionCode?.code as string) || (divisionCode?.code as string),
          schoolLevel:
            (schoolLevel?.code as number) || (schoolLevel?.code as number),
          groupUnitCode:
            (groupUnitCode?.code as string) || (groupUnitCode?.code as string),
          ...pagination,
          skip: pagination.skip + AppConstant.PAGE_SIZE_OPTIONS[0],
          isScroll: true,
        })
      );
    };

    return (
      <Box ref={ref} height="100%" {...otherProps}>
        <AppInfiniteScroll
          height="100%"
          style={{ maxHeight: "300px" }}
          dataLength={truongList.length} //This is important field to render the next data
          next={handleGetMoreData}
          hasMore={true}
        >
          {children}
        </AppInfiniteScroll>
      </Box>
    );
  }
);

type ListBoxInfiniteScrollProps = BoxProps & {
  divisionCode?: IOption | null;
  doetCode?: IOption | null;
  groupUnitCode?: IOption | null;
  schoolLevel?: IOption | null;
};

export default ListBoxInfiniteScroll;
