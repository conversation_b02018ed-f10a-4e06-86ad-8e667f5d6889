"use client";

import React, { memo, useEffect } from "react";
import { Control, UseFormSetValue, useWatch } from "react-hook-form";
import { ILogin } from "../../login.model";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import { DON_VI_LIST, DON_VI_TYPE } from "@/constant/data.const";
import { AppFormAutocomplete, IOption } from "@/components/common";
import { loginActions, loginSelectors } from "../../login.slice";
import { AppConstant } from "@/constant";
import { IDonVi } from "@/models/app.model";
import TruongFilter from "./TruongFilter";

const ChooseDonViSelects = ({
  control,
  onSetValueForm,
}: {
  control: Control<ILogin, any, ILogin>;
  onSetValueForm: UseFormSetValue<ILogin>;
}) => {
  const dispatch = useAppDispatch();
  const domainInfo = useAppSelector((state) => state.appReducer.domainInfo);
  const soList = useAppSelector(loginSelectors.selectSoList);
  const phongList = useAppSelector(loginSelectors.selectPhongList);

  const divisionCode = useWatch({
    control,
    name: "divisionCode",
  });
  const doetCode = useWatch({
    control,
    name: "doetCode",
  });
  const unitLevel = useWatch({
    control,
    name: "unitLevel",
  });

  const handleDoetChange = (data: any) => {
    onSetValueForm("divisionCode", null);
    onSetValueForm("schoolId", null);
    if (data?.code) {
      dispatch(
        loginActions.getTruongList({
          doetCode: data.code,
          ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
        })
      );
    } else {
      dispatch(loginActions.getPhongListSuccess({ data: [], totalCount: 0 }));
      dispatch(loginActions.getTruongListSuccess({ data: [], totalCount: 0 }));
    }
  };

  const handleDivisionChange = (data: any) => {
    onSetValueForm("schoolId", null);
    if (doetCode?.code) {
      dispatch(
        loginActions.getTruongList({
          doetCode: doetCode.code as string,
          divisionCode: data?.code || undefined,
          ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
        })
      );
    } else {
      dispatch(loginActions.getTruongListSuccess({ data: [], totalCount: 0 }));
    }
  };

  useEffect(() => {
    if (doetCode?.code) {
      dispatch(
        loginActions.getTruongList({
          doetCode: doetCode.code as string,
          divisionCode: divisionCode?.code as string,
          ...AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
        })
      );
    }
  }, []);

  useEffect(() => {
    if (doetCode?.code) {
      dispatch(
        loginActions.getPhongList({
          doetCode: doetCode.code as string,
        })
      );
    }
  }, [doetCode]);

  return (
    <>
      {domainInfo?.groupUnitCode !== DON_VI_TYPE.so &&
        domainInfo?.groupUnitCode !== DON_VI_TYPE.phong && (
          <AppFormAutocomplete
            control={control}
            name={"unitLevel"}
            options={convertDonViToOptions(DON_VI_LIST as unknown as IDonVi[])}
            autocompleteProps={{
              textFieldProps: {
                placeholder: "Cấp đơn vị",
              },
            }}
            rules={{
              required: true,
            }}
          />
        )}
      {Boolean(unitLevel) && Boolean(domainInfo?.isShareDomain) && (
        <AppFormAutocomplete
          control={control}
          name={"doetCode"}
          options={convertDonViToOptions(soList)}
          onChangeValueForm={handleDoetChange}
          autocompleteProps={{
            textFieldProps: {
              placeholder: "Sở",
            },
          }}
          rules={{
            required: true,
          }}
        />
      )}
      {(unitLevel?.id === DON_VI_TYPE.phong ||
        unitLevel?.id === DON_VI_TYPE.truong) && (
        <AppFormAutocomplete
          control={control}
          name={"divisionCode"}
          options={convertDonViToOptions(phongList)}
          onChangeValueForm={handleDivisionChange}
          autocompleteProps={{
            textFieldProps: {
              placeholder: "Phòng",
            },
          }}
          rules={{
            required: unitLevel?.id === DON_VI_TYPE.phong,
          }}
        />
      )}
      {unitLevel?.id === DON_VI_TYPE.truong && (
        <TruongFilter
          control={control}
          divisionCode={divisionCode}
          doetCode={doetCode}
        />
      )}
    </>
  );
};

export default memo(ChooseDonViSelects);

export const convertDonViToOptions = (arr: IDonVi[]) => {
  if (Array.isArray(arr)) {
    const newArray = [...arr];

    return newArray?.map((item) => {
      return {
        id: item.id,
        code: item?.schoolCode,
        label:
          item.name + (item.schoolCode ? " (" + item.schoolCode + ")" : ""),
      };
    });
  } else {
    return [];
  }
};
