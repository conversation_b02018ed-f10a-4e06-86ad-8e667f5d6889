"use client";

import React, {
  memo,
  useRef,
  useMemo,
  useCallback,
  JSX,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { ColumnDef, Row, VisibilityState } from "@tanstack/react-table";

import { useTableContext } from "../TableContext";
import { useModalActionContext } from "../ModalActionContext";

import { getElementById } from "@/utils/common.utils";
import { AppCheckbox, AppTable } from "@/components/common";

import HeaderFilter, { HeaderFilterProps } from "./HeaderFilter";
import ModalContainer, {
  ModalContainerProps,
} from "./modal-action/ModalContainer";
import EditCell from "../../table/cell/EditCell";
import { useResizeObserver } from "./hooks/useResizeObserver";
import DeleteCell from "../../table/cell/DeleteCell";
import { AppTableProps } from "../../table/AppTable";
import { ITableRef } from "../type";

const trackedElementId = "table";

export type ContentPageProps<T> = Omit<
  HeaderFilterProps<T>,
  "setColumnVisibility"
> &
  ModalContainerProps<T> & {
    tableProps: Omit<AppTableProps<T>, "data" | "totalData">;
  };

const ContentPage = forwardRef(
  <T,>(
    {
      tableProps,
      actions,
      CreateModalComponent,
      formConfig,
      customActions,
      visibleCol,
    }: ContentPageProps<T>,
    ref: React.Ref<ITableRef>
  ) => {
    const hasUpdate = actions?.includes("update");
    const hasDelete = actions?.includes("delete");
    const hasChecked = actions?.includes("check");

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
      {}
    );

    const { columns, hasDefaultPagination, ...otherTableProps } =
      tableProps || {};

    const resizeElementRef = useRef<HTMLDivElement | null>(null);

    const {
      data,
      totalCount,
      setPagination,
      pagination,
      isLoading,
      fetchCurrentData,
    } = useTableContext<T>();

    useImperativeHandle(ref, () => ({
      fetchCurrentData,
    }));

    const columnsWithIndex: ColumnDef<T>[] = useMemo(() => {
      const baseColumns: ColumnDef<T>[] = [
        {
          id: "index",
          header: "STT",
          size: 50,
          meta: { align: "center" },
          cell: ({ row, table }) => {
            const allRows = hasDefaultPagination
              ? table.getSortedRowModel().rows
              : table.getRowModel().rows;

            const indexMap = generateRowIndexMap(
              allRows,
              pagination.skip,
              hasDefaultPagination
            );

            return indexMap[row.id] || "";
          },
        },
      ];

      const editColumn: ColumnDef<T> = {
        id: "edit",
        header: "Sửa",
        size: 50,
        cell: ({ row }) => <RenderEditCell<T> row={row.original} />,
        meta: { align: "center" },
      };

      const deleteColumn: ColumnDef<T> = {
        id: "delete",
        header: "Xóa",
        size: 50,
        cell: ({ row }) => <RenderDeleteCell<T> row={row.original} />,
        meta: { align: "center" },
      };

      const checkColumn: ColumnDef<T> = {
        id: "select",
        accessorKey: "select",
        meta: {
          align: "center",
        },
        size: 50,
        header: ({ table }) => (
          <AppCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler(),
            }}
          />
        ),
        cell: ({ row }) => (
          <AppCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler(),
            }}
          />
        ),
      };

      return [
        ...baseColumns,
        ...(hasUpdate ? [editColumn] : []),
        ...(hasDelete ? [deleteColumn] : []),
        ...(hasChecked ? [checkColumn] : []),
        ...columns,
      ];
    }, [
      pagination.skip,
      hasUpdate,
      hasDelete,
      hasChecked,
      columns,
      hasDefaultPagination,
    ]);

    const handleResize = useCallback(() => {
      const heightWindow = window.innerHeight;
      const tableEl = getElementById(trackedElementId);
      const paginationEl = getElementById("pagination");

      if (tableEl) {
        const headerHeight = tableEl.getBoundingClientRect().top;
        const paginationHeight =
          paginationEl?.getBoundingClientRect().height || 0;

        tableEl.style.height =
          heightWindow - headerHeight - paginationHeight - 1 + "px";
      }
    }, []);

    useResizeObserver(resizeElementRef, handleResize);

    return (
      <>
        <HeaderFilter<T>
          actions={actions}
          ref={resizeElementRef}
          customActions={customActions}
          visibleCol={visibleCol}
          setColumnVisibility={setColumnVisibility}
        />
        <AppTable
          data={data}
          columns={columnsWithIndex}
          totalData={totalCount}
          onPageChange={hasDefaultPagination ? undefined : setPagination}
          paginationData={hasDefaultPagination ? undefined : pagination}
          isFetching={isLoading}
          columnVisibility={columnVisibility}
          hasDefaultPagination={hasDefaultPagination}
          {...otherTableProps}
        />
        <ModalContainer<T>
          formConfig={formConfig}
          actions={actions}
          CreateModalComponent={CreateModalComponent}
        />
      </>
    );
  }
);

export default memo(ContentPage) as <T>(
  props: ContentPageProps<T> & { ref?: React.Ref<any> }
) => JSX.Element;

type RenderEditCellProps<T> = {
  row?: T;
};
const RawRenderEditCell = <T,>({ row }: RenderEditCellProps<T>) => {
  const { openEditModal } = useModalActionContext();
  return <EditCell onClick={() => openEditModal(row)} />;
};

const RenderEditCell = memo(RawRenderEditCell) as <T>(
  props: RenderEditCellProps<T>
) => JSX.Element;
(RenderEditCell as any).displayName = "RenderEditCell";

type RenderDeleteCellProps<T> = {
  row?: T;
};

const RenderDeleteCellBase = <T,>({ row }: RenderDeleteCellProps<T>) => {
  const { openDeleteModal } = useModalActionContext();
  return <DeleteCell onClick={() => openDeleteModal(row)} />;
};

const RenderDeleteCell = memo(RenderDeleteCellBase) as <T>(
  props: RenderDeleteCellProps<T>
) => JSX.Element;

(RenderDeleteCell as unknown as { displayName: string }).displayName =
  "RenderDeleteCell";

type RowLike = {
  id: string;
  parentId?: string;
  depth: number;
  index: number;
};

export function generateRowIndexMap(
  rows: RowLike[],
  paginationSkip = 0,
  hasDefaultPagination = false
): Record<string, string> {
  const indexMap: Record<string, string> = {};
  const parentCountMap: Record<string, number> = {}; // Track count of children

  for (const row of rows) {
    if (row.depth === 0) {
      // Root row
      const index =
        (hasDefaultPagination ? row.index : rows.indexOf(row)) +
        1 +
        (hasDefaultPagination ? 0 : paginationSkip);
      indexMap[row.id] = `${index}`;
      parentCountMap[row.id] = 0;
    } else {
      const parentId = row.parentId ?? "";
      const parentIndex = indexMap[parentId] || "?";
      parentCountMap[parentId] = (parentCountMap[parentId] ?? 0) + 1;
      const childIndex = parentCountMap[parentId];
      indexMap[row.id] = `${parentIndex}.${childIndex}`;
    }
  }

  return indexMap;
}
