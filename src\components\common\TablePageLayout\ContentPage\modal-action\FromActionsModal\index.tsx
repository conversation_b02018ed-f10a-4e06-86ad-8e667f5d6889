"use client";

import React, { memo, JSX } from "react";
import { Grid } from "@mui/material";
import { Control, FieldValues, useFormState, useWatch } from "react-hook-form";
import GridFormContainer from "@/components/common/GridFormContainer";
import AppFormTextField from "@/components/common/form/AppFormTextField";
import {
  ConditionalDisplayConfig,
  FormFieldConfig,
} from "@/components/common/TablePageLayout/type";
import useGetApiFormFieldOptions from "../useGetApiFormFieldOptions";
import AutoListForm from "./form-field/AutoListForm";
import AppFormControlToggle from "@/components/common/form/AppFormControlToggle";
import AppFormDatePicker from "@/components/common/form/AppFormDatePicker";
import AppFormTextEditor from "@/components/common/form/AppFormTextEditor";
import AppFormRadio from "@/components/common/form/AppFormRadio";

type FromActionsModalProps<T extends FieldValues> = {
  control: Control<T>;
  formField?: FormFieldConfig<T>[];
};

const FromActionsModal = <T extends FieldValues>({
  control,
  formField,
}: FromActionsModalProps<T>) => {
  const { errors } = useFormState({ control });
  const allValues = useWatch({ control });

  const optionsObj = useGetApiFormFieldOptions(formField);

  return (
    <GridFormContainer>
      {formField?.map((item: FormFieldConfig<T>) => {
        if (!shouldDisplay(item.displayIf, allValues as T)) return null;

        return (
          <Grid size={item.size || 12} key={item.key}>
            {item.type === "text" && (
              <AppFormTextField<any>
                control={control}
                textfieldProps={{
                  autoFocus: item.autoFocus,
                  error: Boolean(errors?.[item.key]),
                  helperText: errors?.[item.key]?.message as React.ReactNode,
                  placeholder: item.placeholder,
                  disabled: item.disabled,
                  ...item?.textFieldProps,
                }}
                rules={item.rules}
                label={item.label}
                name={item.key}
              />
            )}
            {item.type === "select" && (
              <AutoListForm<any>
                options={optionsObj[item.key] ?? []}
                formField={item}
                textFieldProps={{
                  autoFocus: item.autoFocus,
                  error: Boolean(errors?.[item.key]),
                  helperText: errors?.[item.key]?.message as React.ReactNode,
                  placeholder: item.placeholder,
                  disabled: item.disabled,
                  ...item?.textFieldProps,
                }}
              />
            )}
            {item.type === "number" && (
              <AppFormTextField<any>
                control={control}
                textfieldProps={{
                  autoFocus: item.autoFocus,
                  error: Boolean(errors?.[item.key]),
                  helperText: errors?.[item.key]?.message as React.ReactNode,
                  placeholder: item.placeholder,
                  type: "number",
                  isDecimal: item?.textFieldNumberProps?.isDecimal,
                  slotProps: {
                    htmlInput: {
                      min: item?.textFieldNumberProps?.min,
                      max: item?.textFieldNumberProps?.max,
                      step: item?.textFieldNumberProps?.step,
                    },
                  },

                  disabled: item.disabled,
                  ...item?.textFieldProps,
                }}
                rules={item.rules}
                label={item.label}
                name={item.key}
              />
            )}
            {item.type === "toggle" && (
              <AppFormControlToggle
                label={item.label}
                control={control}
                name={item.key}
                toggleProps={{
                  disabled: item.disabled,
                }}
              />
            )}
            {item.type === "date" && (
              <AppFormDatePicker
                label={item.label}
                control={control}
                name={item.key}
                datePickerProps={{
                  disabled: item.disabled,
                  ...item.dataPickerProps,
                }}
              />
            )}
            {item.type === "editor" && (
              <AppFormTextEditor
                label={item.label}
                control={control}
                name={item.key}
                rules={item.rules}
              />
            )}
            {item.type === "radio" && (
              <AppFormRadio
                label={item.label}
                control={control}
                name={item.key}
                rules={item.rules}
                radioList={optionsObj?.[item.key] ?? []}
              />
            )}
            {item.type === "custom" && item.component?.(control)}
          </Grid>
        );
      })}
    </GridFormContainer>
  );
};

export default memo(FromActionsModal) as <T extends FieldValues>(
  props: FromActionsModalProps<T>
) => JSX.Element;

const shouldDisplay = <T extends FieldValues>(
  displayIf: ConditionalDisplayConfig<T>[] | undefined,
  allValues: T
): boolean => {
  if (!displayIf || displayIf.length === 0) return true;
  return displayIf.every(({ key, value, notEqual }) => {
    const current = allValues[key];
    return notEqual ? current !== value : current === value;
  });
};
