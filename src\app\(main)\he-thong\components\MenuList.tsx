"use client";

import React, { memo } from "react";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stack,
} from "@mui/material";
import { ArrowIcon } from "@/components/icons";
import { IMenuGroup, IMenuItem } from "./MenuData";
import MenuCard from "./MenuCard";

interface MenuListProps {
  groups: IMenuGroup[];
  onItemClick?: (item: IMenuItem) => void;
}

const MenuList: React.FC<MenuListProps> = ({ groups, onItemClick }) => {
  return (
    <Box sx={{ p: 3 }}>
      {groups.map((group, index) => (
        <Accordion
          key={group.id}
          defaultExpanded={index === 0}
          sx={{
            mb: 2,
            borderRadius: 2,
            "&:before": {
              display: "none",
            },
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          }}
        >
          <AccordionSummary
            expandIcon={
              <ArrowIcon
                sx={{
                  fontSize: 20,
                  color: "text.secondary",
                }}
              />
            }
            sx={{
              backgroundColor: "grey.50",
              borderRadius: "8px 8px 0 0",
              "&.Mui-expanded": {
                borderRadius: "8px 8px 0 0",
              },
            }}
          >
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: "text.primary",
                  fontSize: "1.1rem",
                }}
              >
                {group.name}
              </Typography>
              {group.description && (
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                    fontSize: "0.875rem",
                    mt: 0.5,
                  }}
                >
                  {group.description}
                </Typography>
              )}
            </Box>
          </AccordionSummary>

          <AccordionDetails
            sx={{
              p: 3,
              backgroundColor: "background.paper",
            }}
          >
            <Stack spacing={1}>
              {group.items.map((item) => (
                <MenuCard
                  key={item.id}
                  item={item}
                  variant="list"
                  onClick={onItemClick}
                />
              ))}
            </Stack>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default memo(MenuList);
