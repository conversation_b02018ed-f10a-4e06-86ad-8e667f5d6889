"use client";

import React, { memo } from "react";
import {
  Box,
  Typography,
  Grid,
  Stack,
  Card,
  CardActionArea,
} from "@mui/material";
import { ArrowIcon, SchoolIcon } from "@/components/icons";
import { AppLink } from "@/components/common";
import { IMenuGroup, IMenuItem } from "./MenuData";

interface MenuListProps {
  groups: IMenuGroup[];
  onItemClick?: (item: IMenuItem) => void;
}

const MenuList: React.FC<MenuListProps> = ({ groups, onItemClick }) => {
  const handleItemClick = (item: IMenuItem) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  return (
    <Box sx={{ p: 3, backgroundColor: "#f5f5f5", minHeight: "100vh" }}>
      <Grid container spacing={3}>
        {groups.map((group) => (
          <Grid size={{ xs: 12, md: 4 }} key={group.id}>
            <Box>
              {/* Group Header */}
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: "text.primary",
                  mb: 2,
                  fontSize: "1rem",
                }}
              >
                {group.name}
              </Typography>

              {/* Group Items */}
              <Stack spacing={1}>
                {group.items.map((item) => (
                  <Card
                    key={item.id}
                    sx={{
                      borderRadius: 1,
                      boxShadow: "none",
                      border: "1px solid",
                      borderColor: "grey.200",
                      "&:hover": {
                        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                        borderColor: "primary.main",
                      },
                      transition: "all 0.2s ease-in-out",
                    }}
                  >
                    <CardActionArea
                      component={AppLink}
                      href={item.href}
                      onClick={() => handleItemClick(item)}
                      sx={{
                        p: 2,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", flex: 1 }}
                      >
                        <Box
                          sx={{
                            width: 32,
                            height: 32,
                            borderRadius: "50%",
                            backgroundColor: "primary.light",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            mr: 2,
                          }}
                        >
                          <SchoolIcon
                            sx={{
                              fontSize: 16,
                              color: "primary.main",
                            }}
                          />
                        </Box>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 500,
                            color: "text.primary",
                            fontSize: "0.875rem",
                            lineHeight: 1.4,
                          }}
                        >
                          {item.name}
                        </Typography>
                      </Box>
                      <ArrowIcon
                        sx={{
                          fontSize: 16,
                          color: "text.secondary",
                          transform: "rotate(-90deg)",
                        }}
                      />
                    </CardActionArea>
                  </Card>
                ))}
              </Stack>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default memo(MenuList);
