import { ComponentType, JSX, memo } from "react";
import { useModalActionContext } from "../../ModalActionContext";
import { ActionType, FormConfig } from "../../type";
import dynamic from "next/dynamic";
import { AppModalProps } from "@/components/common/modal/AppModal";
import DeleteModal from "./DeleteModal";
import { useTableContext } from "../../TableContext";

const CreateModal = dynamic(() => import("../modal-action/CreateModal"), {
  ssr: false,
});
const EditModal = dynamic(() => import("../modal-action/EditModal"), {
  ssr: false,
});

const ModalContainer = <T,>({
  actions,
  formConfig,
  CreateModalComponent,
  EditModalComponent,
}: ModalContainerProps<T>) => {
  const { isOpenCreate, isOpenEdit, isOpenDelete, closeModal } =
    useModalActionContext();
  const { fetchCurrentData } = useTableContext();

  return (
    <>
      {actions?.includes("create") &&
        isOpenCreate &&
        (CreateModalComponent ? (
          <CreateModalComponent
            isOpen={isOpenCreate}
            onClose={closeModal}
            fetchCurrentData={fetchCurrentData}
          />
        ) : (
          <CreateModal
            isOpen={isOpenCreate}
            onClose={closeModal}
            formConfig={formConfig as any}
          />
        ))}
      {actions?.includes("update") &&
        isOpenEdit &&
        (EditModalComponent ? (
          <EditModalComponent
            isOpen={isOpenEdit}
            onClose={closeModal}
            fetchCurrentData={fetchCurrentData}
          />
        ) : (
          <EditModal
            isOpen={isOpenEdit}
            formConfig={formConfig as any}
            onClose={closeModal}
          />
        ))}
      {actions?.includes("delete") && isOpenDelete && (
        <DeleteModal url={formConfig?.deleteUrl} />
      )}
    </>
  );
};

export type ModalContainerProps<T> = {
  actions?: ActionType[];
  formConfig?: FormConfig<T>;
  CreateModalComponent?: ComponentType<
    AppModalProps & {
      /** Hàm get lại dữ liệu bảng */
      fetchCurrentData?: () => void;
    }
  >;
  EditModalComponent?: ComponentType<
    AppModalProps & {
      fetchCurrentData?: () => void;
    }
  >;
};
export default memo(ModalContainer) as <T>(
  props: ModalContainerProps<T>
) => JSX.Element;
