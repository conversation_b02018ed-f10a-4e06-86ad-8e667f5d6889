"use client";

import { TableProvider } from "./TableContext"; // <PERSON><PERSON>m bảo import đúng
import ContentPage, { ContentPageProps } from "./ContentPage";
import { ITableRef, TableProviderProps } from "./type";
import { ModalActionProvider } from "./ModalActionContext";
import React, { JSX, memo, forwardRef } from "react";

const TablePageLayout = forwardRef(
  <T,>(
    {
      apiUrl,
      tableProps,
      filterConfig,
      formConfig,
      actions,
      CreateModalComponent,
      formatData,
      fetchAll,
      customActions,
      visibleCol,
      cleanDataFormFiled,
    }: TablePageLayoutProps<T>,
    ref: React.Ref<ITableRef>
  ) => {
    return (
      <TableProvider<T>
        apiUrl={apiUrl}
        formatData={formatData}
        filterConfig={filterConfig}
        fetchAll={fetchAll}
        cleanDataFormFiled={cleanDataFormFiled}
      >
        <ModalActionProvider detailUrl={formConfig?.detailUrl}>
          <ContentPage<T>
            ref={ref}
            formConfig={formConfig}
            actions={actions}
            tableProps={tableProps}
            CreateModalComponent={CreateModalComponent}
            customActions={customActions}
            visibleCol={visibleCol}
          />
        </ModalActionProvider>
      </TableProvider>
    );
  }
);

// Export
export default memo(TablePageLayout) as <T>(
  props: TablePageLayoutProps<T> & { ref?: React.Ref<ITableRef> }
) => JSX.Element;

type TablePageLayoutProps<T> = ContentPageProps<T> &
  Omit<TableProviderProps<T>, "children"> & {};
