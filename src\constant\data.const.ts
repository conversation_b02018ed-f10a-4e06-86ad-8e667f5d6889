export enum STATUS_TYPE {
  inActive,
  active,
}

export enum BOOLEAN_TYPE {
  false,
  true,
}

export enum SCHOOL_LEVEL {
  mamNon = 1,
  tieuHoc = 2,
  thcs = 4,
  thpt = 8,
  gdtx = 16,
}

export enum CATEGORY_ID {
  none = 0,
  paper,
  ebook,
  audioBook,
  album,
  video,
  electure,
  skill,
  otherDocument,
  emagazine,
  interactiveBook,
  news,
}

export enum DON_VI_TYPE {
  bo = "01",
  so = "02",
  phong = "03",
  truong = "04",
  doitac = "05",
}

export const STATUS_TYPE_LIST = [
  {
    id: STATUS_TYPE.active,
    label: "Hiển thị",
  },
  {
    id: STATUS_TYPE.inActive,
    label: "Không hiển thị",
  },
];

export const DON_VI_LIST = [
  {
    id: DON_VI_TYPE.bo,
    name: "<PERSON><PERSON>",
    label: "Bộ",
    schoolCode: DON_VI_TYPE.bo,
  },
  {
    id: DON_VI_TYPE.so,
    name: "Sở",
    label: "Sở",
    schoolCode: DON_VI_TYPE.so,
  },
  {
    id: DON_VI_TYPE.phong,
    name: "<PERSON>ò<PERSON>",
    label: "Phòng",
    schoolCode: DON_VI_TYPE.phong,
  },
  {
    id: DON_VI_TYPE.truong,
    name: "Trường",
    label: "Trường",
    schoolCode: DON_VI_TYPE.truong,
  },
];
