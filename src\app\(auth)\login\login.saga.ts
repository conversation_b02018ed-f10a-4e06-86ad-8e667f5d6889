import { loginActions } from "@/app/(auth)/login/login.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { IDonviParams } from "./login.model";
import { DataListResponseModel } from "@/models/response.model";
import { IDonVi } from "@/models/app.model";
import { AppServices } from "@/services";
import { ApiConstant, DataConstant, EnvConstant } from "@/constant";

function* getTruongListSaga(
  action: PayloadAction<
    IDonviParams & {
      isScroll?: boolean;
    }
  >
) {
  try {
    const response: DataListResponseModel<IDonVi> = yield call(
      AppServices.getDonViListService,
      {
        ...action.payload,
        groupUnitCode: DataConstant.DON_VI_TYPE.truong,
      }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        loginActions.getTruongListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
          isScroll: action.payload.isScroll,
        })
      );
    } else {
      yield put(
        loginActions.getTruongListSuccess({
          data: [],
          totalCount: 0,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      loginActions.getTruongListSuccess({
        data: [],
        totalCount: 0,
      })
    );
  }
}

function* getPhongListLoginSaga(action: PayloadAction<IDonviParams>) {
  try {
    const response: DataListResponseModel<IDonVi> = yield call(
      AppServices.getDonViListService,
      { ...action.payload, groupUnitCode: DataConstant.DON_VI_TYPE.phong }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        loginActions.getPhongListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
    } else {
      yield put(
        loginActions.getPhongListSuccess({
          data: [],
          totalCount: 0,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
    yield put(
      loginActions.getPhongListSuccess({
        data: [],
        totalCount: 0,
      })
    );
  }
}

function* getSoListSaga() {
  try {
    const response: DataListResponseModel<IDonVi> = yield call(
      AppServices.getDonViListService,
      {
        groupUnitCode: DataConstant.DON_VI_TYPE.so,
      }
    );

    if (response.code === ApiConstant.ERROR_CODE_OK) {
      yield put(
        loginActions.getSoListSuccess({
          data: response.data.data || [],
          totalCount: response.data.totalCount,
        })
      );
    }
  } catch (error) {
    EnvConstant.IS_DEV && console.log(error);
  }
}

export function* loginSaga() {
  yield takeLatest(loginActions.getSoList, getSoListSaga);
  yield takeLatest(loginActions.getTruongList, getTruongListSaga);
  yield takeLatest(loginActions.getPhongList, getPhongListLoginSaga);
}
