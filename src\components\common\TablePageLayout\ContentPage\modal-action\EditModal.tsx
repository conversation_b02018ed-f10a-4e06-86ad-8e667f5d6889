"use client";

import AppModal from "@/components/common/modal/AppModal";
import React, { memo, JSX, useEffect } from "react";
import { FormConfig } from "../../type";
import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";
import FromActionsModal from "./FromActionsModal";
import useActionsData from "./hooks/useActionsData";
import useDefaultFormValues from "./hooks/useDefaultFormValues";
import { useModalActionContext } from "../../ModalActionContext";
import { DEFAULT_UNIX } from "@/components/common/AppAutoComplete";

const EditModal = <T,>({ isOpen, onClose, formConfig }: EditModalProps<T>) => {
  const { handleEditData } = useActionsData<T>();
  const { modalData } = useModalActionContext();

  const defaultValues = useDefaultFormValues(formConfig?.updateFields);

  const methods = useForm({
    defaultValues,
  });

  const { control, handleSubmit, reset, setValue } = methods;

  const handleClose = () => {
    onClose();
    reset(defaultValues);
  };

  const handleSubmitData = (data: Record<string, any>) => {
    handleEditData(
      { id: (modalData as any)?.id, ...data },
      formConfig,
      handleClose
    );
  };

  useEffect(() => {
    if (isOpen) {
      Object.keys(defaultValues).forEach((key) => {
        const newValue = (modalData as any)?.[key];
        const currentKey = formConfig?.updateFields?.find(
          (item) => item.key === key
        );

        if (newValue !== undefined) {
          if (currentKey?.type === "select") {
            if (currentKey.selectConfig?.isMulti) {
              const multiVal = newValue.map(
                (item: any) =>
                  item?.[currentKey?.selectConfig?.valueKey || DEFAULT_UNIX]
              );
              setValue(key, multiVal);
            } else {
              setValue(key, newValue);
            }
          } else {
            setValue(key, newValue);
          }
        }
      });
    }
  }, [isOpen, modalData]);

  return (
    <FormProvider {...methods}>
      <AppModal
        onSubmit={handleSubmit(handleSubmitData)}
        isOpen={isOpen}
        onClose={handleClose}
        modalTitleProps={{
          title: "Chỉnh sửa",
        }}
        slotProps={{
          paper: {
            component: "form",
          },
        }}
        modalContentProps={{
          content: (
            <FromActionsModal<any>
              control={control}
              formField={formConfig?.updateFields}
            />
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button variant="outlined" onClick={handleClose}>
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
        {...formConfig?.modalProps}
      />
    </FormProvider>
  );
};

type EditModalProps<T> = {
  isOpen: boolean;
  onClose: () => void;
  formConfig?: FormConfig<T>;
};

export default memo(EditModal) as <T>(props: EditModalProps<T>) => JSX.Element;
