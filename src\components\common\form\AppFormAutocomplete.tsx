"use client";

import { InputLabel, InputLabelProps, Stack, StackProps } from "@mui/material";
import { ReactNode, memo, JSX } from "react";
import {
  Control,
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  Path,
  RegisterOptions,
} from "react-hook-form";
import AppAutoComplete, {
  AppAutoCompleteProps,
  IOption,
} from "../AppAutoComplete";
import { FilterValueType } from "../TablePageLayout/type";

const AppFormAutocomplete = <T extends FieldValues>({
  label,
  options,
  control,
  name,
  rules,
  isMultiple = false,
  controlProps,
  labelProps,
  actionsButton,
  autocompleteProps,
  onChangeValueForm,
  ...otherProps
}: AppFormAutocompleteProps<T>) => {
  return (
    <Stack width="100%" {...otherProps}>
      {label && (
        <InputLabel
          sx={{ mb: 0.5 }}
          required={Boolean(rules?.required)}
          {...labelProps}
        >
          {label}
        </InputLabel>
      )}
      <Stack alignItems="center" direction="row">
        <Controller
          control={control}
          name={name}
          rules={rules}
          render={({ field: { onChange, value, ref, ...restField } }) => {
            return (
              <AppAutoComplete
                {...restField}
                value={value}
                disableCloseOnSelect={isMultiple}
                multiple={isMultiple}
                options={options}
                onChange={(_, data) => {
                  onChange(data);
                  if (onChangeValueForm instanceof Function) {
                    onChangeValueForm(data);
                  }
                }}
                {...autocompleteProps}
                textFieldProps={{
                  ...autocompleteProps?.textFieldProps,
                }}
              />
            );
          }}
          {...controlProps}
        />
        {actionsButton}
      </Stack>
    </Stack>
  );
};

export type AppFormAutocompleteProps<T extends FieldValues> = StackProps & {
  label?: string;
  options: IOption[];
  control: Control<T>;
  name: FieldPath<T>;

  isMultiple?: boolean;
  rules?: Omit<
    RegisterOptions<T, Path<T>>,
    "disabled" | "valueAsNumber" | "valueAsDate" | "setValueAs"
  >;
  controlProps?: Omit<ControllerProps<T>, "render" | "name" | "control">;
  actionsButton?: ReactNode;

  labelProps?: InputLabelProps;
  autocompleteProps?: Omit<AppAutoCompleteProps, "options">;

  onChangeValueForm?: (data?: IOption | IOption[] | FilterValueType) => void;
};

export default memo(AppFormAutocomplete) as <T extends FieldValues>(
  props: AppFormAutocompleteProps<T>
) => JSX.Element;
