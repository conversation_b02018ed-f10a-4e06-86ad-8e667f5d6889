import { DataConstant } from "@/constant";

export interface IMenu {
  id: number;
  name: string;
  icon: string;
  parentId: number;
  href: string;
  order: number;
}

export interface IMenuItemTree {
  isEnable: boolean;
  schoolMenuConfigId: number | null;
  children: IMenuItemTree[];
  name: string;
  icon: string;
  status: DataConstant.STATUS_TYPE;
  parentId: number;
  menuTypeId: number;
  href: string;
  order: number;
  id: number;
  createdBy: number;
  updatedBy: number | null;
  createdAt: string;
  updatedAt: string | null;
  listGroupsUnitCode: string[];
  isSystem: DataConstant.BOOLEAN_TYPE;
  isRoot: DataConstant.BOOLEAN_TYPE;
  groupUnitCode: string;
  isShowSubMenu: DataConstant.BOOLEAN_TYPE;
  isBetaMenu: DataConstant.BOOLEAN_TYPE;
}

export enum MENU_TYPE {
  SYSTEM_MANAGEMENT_CMS = 1,
  HOME,
  LIST_OF_DOCUMENTS,
}
