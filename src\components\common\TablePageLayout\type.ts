import { IPaginationModel } from "@/models/response.model";
import { IOption } from "../AppAutoComplete";
import { Control, FieldValues, Path, RegisterOptions } from "react-hook-form";
import { TextFieldProps } from "@mui/material";
import { DatePickerProps } from "@mui/x-date-pickers";
import { Dayjs } from "dayjs";
import { ReactNode } from "react";
import { AppModalProps } from "../modal/AppModal";

export type FilterValueType =
  | string
  | number
  | Date
  | IOption
  | null
  | IOption[]
  | Array<any>;

export type ActionType = "delete" | "update" | "create" | "check";

export type ConditionalDisplayConfig<T> = {
  key: keyof T; // theo dõi key nào
  value: any; // hiển thị khi giá trị === value
  notEqual?: boolean; // nếu là true, hiển thị khi khác value
};

export interface FormFieldConfig<T> {
  /** Tên trường dữ liệu, tương <PERSON>ng với key trong model T */
  key: Path<T>;

  /** Nhãn hiển thị trong form */
  label?: string;

  /** Loại input hiển thị: text, number, select, date, editor, toggle */
  type:
    | "text"
    | "number"
    | "select"
    | "date"
    | "editor"
    | "toggle"
    | "radio"
    | "custom";

  /** Giá trị khởi tạo */
  defaultValue?: string | number | IOption[] | Dayjs | null;

  /** API endpoint để fetch options nếu là select động */
  apiListUrl?: string;

  /** Placeholder hiển thị trong input */
  placeholder?: string;

  /** Vô hiệu hóa input này */
  disabled?: boolean;

  /** Các rule xác thực (dùng cho react-hook-form) */
  rules?: RegisterOptions<FieldValues>;

  /** Kích thước của field trên grid, nếu có dùng layout responsive (vd: size = 6 là chiếm 6/12) */
  size?: number;

  /** Ưu tiên focus */
  autoFocus?: boolean;

  /** component render custom với type custom */
  component?: (control: Control<any>) => ReactNode;

  selectConfig?: {
    /** Cho phép chọn nhiều giá trị hay không (dành cho select) */
    isMulti?: boolean;

    /** key lấy trong options (id, code) */
    valueKey?: string;

    /** Danh sách option nếu là trường select (tĩnh), key chính là key hoặc code) */
    options?: IOption[];
  };

  textFieldNumberProps?: {
    /**Cho phép nhập số thập phân */
    isDecimal?: boolean;

    /** Giá trị tối đa cho phép nhập vào */
    max?: number;
    /** Giá trị tối thiểu cho phép nhập vào mặc định là 0 */
    min?: number;
    /** Bước nhảy cho giá trị nhập vào mặc định là 1 */
    step?: number;
  };

  /** Props cho textfield */
  textFieldProps?: TextFieldProps;

  dataPickerProps?: DatePickerProps<any>;

  displayIf?: ConditionalDisplayConfig<T>[];
}

export interface TableState<T> {
  /** Dữ liệu hiển thị trên bảng */
  data: T[];

  /** Trạng thái loading khi đang fetch dữ liệu */
  isLoading: boolean;

  /** Thông báo lỗi (nếu có) khi lấy dữ liệu */
  error: string | null;

  /** Tổng số bản ghi (dùng cho phân trang) */
  totalCount: number;

  /** Thông tin phân trang hiện tại (skip, take, ...) */
  pagination: IPaginationModel;

  /** Danh sách cấu hình các filter đang sử dụng */
  filter: FilterConfig[] | null;

  /** Hàm cập nhật phân trang */
  setPagination: (page: IPaginationModel) => void;

  /** Hàm thay đổi giá trị của một filter theo `key` */
  handleChangeFilter: (key: string) => (value: FilterValueType) => void;

  /** Hàm reset toàn bộ filter về mặc định */
  handleClearFilter: () => void;

  /** Danh sách các options tương ứng cho từng filter kiểu select theo keyfilter */
  filterOptions: Record<string, any[]>;

  /** hàm fetch list data */
  fetchCurrentData?: () => void;

  /** Clean data trước khi gọi api */
  cleanDataFormFiled?: (data: T) => any;
}

export interface FilterConfig {
  /** Khóa định danh filter, dùng làm key truyền lên API  */
  key: string;

  /**
   * Khóa (key) được chọn hiện tại, dùng để xác định mục đã được chọn trong danh sách.
   * Thường được sử dụng để highlight hoặc thực hiện thao tác với mục đã chọn.
   */
  selectedKey?: string;

  /** Loại filter:
   * - 'select': dropdown chọn
   * - 'text': ô nhập text
   * - 'date': chọn ngày đơn
   * - 'dateRange': khoảng ngày
   */
  type?: "select" | "text" | "date" | "dateRange";

  /** Giá trị hiện tại đang chọn của filter */
  value?: FilterValueType;

  /** Kích thước (grid size) của filter trên giao diện (thường từ 1 đến 12) */
  size?: number;

  /** Nhãn hiển thị trên UI
   * Bắt buộc nếu `type` được khai báo (dùng để hiển thị giao diện).
   */
  label?: string;

  /** Có phải filter nâng cao hay không (để tách biệt hiển thị nâng cao/cơ bản) */
  isAdvanced?: boolean;

  /** API dùng để lấy danh sách chọn (dành cho select) */
  apiListUrl?: string;

  /** Cho phép chọn nhiều giá trị hay không (dành cho select) */
  isMulti?: boolean;

  /** Trường dùng để lấy giá trị khi chọn nhiều (ví dụ 'id', 'code') */
  multiKey?: string;

  /** Danh sách option nếu là trường select (tĩnh) */
  options?: IOption[];

  /** Props truyền cho filter tương ứng (input, select,..) */
  fieldProps?: any;

  keyDateRange?: string[];
}

export type TableProviderProps<T> = {
  /** Các component con nằm trong context của bảng */
  children: React.ReactNode;

  /** API URL chính để fetch dữ liệu bảng */
  apiUrl?: string;

  /** Cấu hình các filter dùng để lọc dữ liệu */
  filterConfig?: FilterConfig[];

  /** lấy tất cả data, mặc định là true */
  fetchAll?: boolean;

  /** format data sau khi gọi api */
  formatData?: (data: T[]) => any[];

  /** Clean data trước khi gọi api */
  cleanDataFormFiled?: (data: T) => any;
};

export type FormConfig<T> = {
  modalProps?: Omit<AppModalProps, "isOpen" | "onClose">;
  /** Cấu hình cho form thêm/sửa và các endpoint tương ứng */
  /** Danh sách field hiển thị khi tạo mới */
  createFields?: FormFieldConfig<T>[];

  /** Danh sách field hiển thị khi cập nhật */
  updateFields?: FormFieldConfig<T>[];

  /** API endpoint để tạo mới */
  createUrl?: string;

  /** API endpoint để cập nhật */
  updateUrl?: string;

  /** API endpoint để xóa */
  deleteUrl?: string;

  detailUrl?: string;
};

export interface ITableRef {
  /** hàm gọi lại api list */
  fetchCurrentData?: () => void;
}
