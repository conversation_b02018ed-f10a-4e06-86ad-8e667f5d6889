import http from "@/api";
import { ApiConstant, AppConstant } from "@/constant";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import React, {
  createContext,
  useContext,
  useEffect,
  useCallback,
} from "react";
import { useAtom } from "jotai";
import equal from "fast-deep-equal";
import { atom } from "jotai";
import { buildFilterParamsFromConfig, handleGetApiOptions } from "./helper";
import {
  FilterConfig,
  FilterValueType,
  TableProviderProps,
  TableState,
} from "./type";
import { toast } from "sonner";
import { CANCEL_MSG } from "@/constant/api.const";

export const DEFAULT_MULTI_KEY = "id";

export const dataAtom = atom<any[]>([]);
export const isLoadingAtom = atom(false);
export const errorAtom = atom<string | null>(null);
export const totalCountAtom = atom(0);
export const paginationAtom = atom<IPaginationModel>(
  AppConstant.DEFAULT_PAGINATION_SKIP_TAKE
);
export const filterAtom = atom<FilterConfig[] | null>(null);
export const filterOptionsAtom = atom<Record<string, any[]>>({});

const TableContext = createContext<TableState<any> | undefined>(undefined);

export const TableProvider = <T,>({
  children,
  apiUrl,
  filterConfig = [],
  formatData,
  fetchAll = false,
  cleanDataFormFiled,
}: TableProviderProps<T>) => {
  const [data, setData] = useAtom(dataAtom);
  const [isLoading, setIsLoading] = useAtom(isLoadingAtom);
  const [error, setError] = useAtom(errorAtom);
  const [totalCount, setTotalCount] = useAtom(totalCountAtom);
  const [pagination, setPagination] = useAtom(paginationAtom);
  const [filter, setFilter] = useAtom(filterAtom);
  const [filterOptions, setFilterOptions] = useAtom(filterOptionsAtom);

  const handleChangeFilter = useCallback(
    (key: string) => (value: FilterValueType) => {
      if (key)
        setFilter((prev) => {
          return (
            prev?.map((item) =>
              item.key === key ? { ...item, value } : item
            ) || []
          );
        });
    },
    [setFilter]
  );

  const handleClearFilter = useCallback(() => {
    setFilter(filterConfig);
    setPagination(AppConstant.DEFAULT_PAGINATION_SKIP_TAKE);
  }, [filterConfig]);

  const fetchData = useCallback(
    (() => {
      let latestRequestId = 0;

      return async ({
        apiUrl,
        controller,
        pagination,
        filter,
      }: {
        apiUrl: string;
        controller: AbortController;
        pagination: IPaginationModel;
        filter: { [x: string]: any };
      }) => {
        const currentRequestId = ++latestRequestId;

        setIsLoading(true);
        setError(null);

        try {
          const params = {
            ...(fetchAll ? {} : { ...pagination }),
            ...filter,
          };

          const response = await http.get<DataListResponseModel<T>>(apiUrl, {
            controller,
            params,
          });

          if (currentRequestId !== latestRequestId) return; // bỏ qua kết quả cũ

          if (response.code === ApiConstant.ERROR_CODE_OK) {
            const dataFormat = formatData
              ? formatData(response.data.data)
              : response.data.data;
            setData(dataFormat);
            setTotalCount(response.data.totalCount);
          } else {
            throw new Error("Failed to fetch data");
          }
        } catch (err: any) {
          if (err?.name === "AbortError" || err?.message === CANCEL_MSG) {
            console.warn("🛑 Request bị hủy (aborted):", apiUrl);
            return;
          } else {
            setError(err?.message || "Đã xảy ra lỗi không xác định");
            toast.error("Thất bại!", {
              description:
                typeof err?.payload === "string"
                  ? err.payload
                  : JSON.stringify(err?.payload) || "Lỗi khi lấy dữ liệu",
            });
          }
        } finally {
          if (currentRequestId === latestRequestId) {
            setIsLoading(false);
          }
        }
      };
    })(),
    [fetchAll]
  );

  const fetchCurrentData = useCallback(() => {
    if (!apiUrl) return;
    const controller = new AbortController();

    if (filter?.length !== filterConfig?.length) return;

    const newFilter = buildFilterParamsFromConfig(filter);

    fetchData({ apiUrl, controller, pagination, filter: newFilter });

    return () => {
      controller.abort();
    };
  }, [apiUrl, pagination, filter]);

  useEffect(() => {
    if (!equal(filter, filterConfig)) {
      setFilter(filterConfig);
    }
  }, [filterConfig]);

  useEffect(() => {
    handleGetApiOptions(filterConfig, setFilterOptions);
  }, [filterConfig]);

  useEffect(() => {
    const abort = fetchCurrentData();
    return () => abort?.();
  }, [fetchCurrentData]);

  useEffect(() => {
    return () => {
      setData([]);
      setError(null);
      setTotalCount(0);
      setPagination(AppConstant.DEFAULT_PAGINATION_SKIP_TAKE);
      setFilter(null);
      setFilterOptions({});
    };
  }, []);

  return (
    <TableContext.Provider
      value={{
        data,
        isLoading,
        error,
        totalCount,
        pagination,
        setPagination,
        filter,
        handleChangeFilter,
        handleClearFilter,
        filterOptions,
        fetchCurrentData,
        cleanDataFormFiled,
      }}
    >
      {children}
    </TableContext.Provider>
  );
};

export const useTableContext = <T,>() => {
  const context = useContext(TableContext);
  if (!context) {
    if (process.env.NODE_ENV !== "production") {
      console.error("useTableContext must be used inside TableProvider");
    }
    throw new Error("useTableContext must be used within a TableProvider");
  }
  return context as TableState<T>;
};
