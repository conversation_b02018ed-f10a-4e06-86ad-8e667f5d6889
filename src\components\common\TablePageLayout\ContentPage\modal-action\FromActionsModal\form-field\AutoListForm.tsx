"use client";

import React, { memo, JSX, useMemo } from "react";
import { TextFieldProps } from "@mui/material";
import { FieldValues, useFormContext, useWatch } from "react-hook-form";
import { DEFAULT_UNIX, IOption } from "@/components/common/AppAutoComplete";
import AppFormAutocomplete from "@/components/common/form/AppFormAutocomplete";
import { FilterValueType, FormFieldConfig } from "../../../../type";

const AutoListForm = <T extends FieldValues>({
  options,
  textFieldProps,
  formField,
  ...otherProps
}: AutoListFormProps<T>) => {
  const { setValue, control } = useFormContext<T>();
  const value = useWatch({ name: formField.key }) ?? null;

  const keySelect = formField?.selectConfig?.valueKey || DEFAULT_UNIX;

  const isMulti = formField?.selectConfig?.isMulti;
  const name = formField?.key as any;

  const selectedValue = useMemo(() => {
    if (isMulti) {
      if (!Array.isArray(value)) return [];

      return (value as IOption[])
        .map((val) => options.find((opt) => opt?.[keySelect] === val))
        .filter(Boolean) as IOption[];
    }
    if (typeof value === "object" && value !== null && DEFAULT_UNIX in value)
      return value as IOption;
    return (
      options.find((opt) => opt.id === value || opt.code === value) ?? null
    );
  }, [value, options, isMulti]);

  const handleChange = (newValue?: IOption | IOption[] | FilterValueType) => {
    const newVal = isMulti
      ? (newValue as IOption[]).map((item) => item?.[keySelect] ?? item)
      : (newValue as IOption)?.[keySelect] ?? null;

    setValue(name, newVal as any, { shouldValidate: true });
  };

  return (
    <AppFormAutocomplete
      control={control}
      name={name}
      rules={formField?.rules as any}
      options={options}
      label={formField?.label}
      onChangeValueForm={handleChange}
      autocompleteProps={{
        multiple: isMulti,
        value: selectedValue,
        limitTags: isMulti ? 3 : undefined,
        disableCloseOnSelect: isMulti,
        textFieldProps: textFieldProps,
      }}
      {...otherProps}
    />
  );
};

type AutoListFormProps<T extends FieldValues> = {
  options: IOption[];
  formField: FormFieldConfig<T>;
  textFieldProps?: TextFieldProps;
};

export default memo(AutoListForm) as <T extends FieldValues>(
  props: AutoListFormProps<T>
) => JSX.Element;
