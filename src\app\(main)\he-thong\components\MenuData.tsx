// Menu data interfaces and mock data for the menu system
export interface IMenuItem {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  href: string;
  order: number;
  groupId: number;
  isActive: boolean;
}

export interface IMenuGroup {
  id: number;
  name: string;
  description?: string;
  order: number;
  items: IMenuItem[];
}

// ViewMode enum is now defined in Menu.tsx component

// Mock data for menu items
export const mockMenuData: IMenuGroup[] = [
  {
    id: 1,
    name: "11.1 Quản lý người dùng",
    description: "Quản lý thông tin người dùng trong hệ thống",
    order: 1,
    items: [
      {
        id: 1,
        name: "11.1.1 Quản lý tài khoản",
        description: "Quản lý tài khoản người dùng",
        icon: "account",
        href: "/he-thong/tai-khoan",
        order: 1,
        groupId: 1,
        isActive: true,
      },
      {
        id: 2,
        name: "11.1.2 <PERSON><PERSON><PERSON><PERSON> lý nhóm người dùng",
        description: "Quản lý nhóm và phân quyền người dùng",
        icon: "group",
        href: "/he-thong/nhom-nguoi-dung",
        order: 2,
        groupId: 1,
        isActive: true,
      },
      {
        id: 3,
        name: "11.1.3 Phân quyền nhóm người dùng",
        description: "Thiết lập quyền hạn cho nhóm người dùng",
        icon: "permission",
        href: "/he-thong/phan-quyen",
        order: 3,
        groupId: 1,
        isActive: true,
      },
    ],
  },
  {
    id: 2,
    name: "11.2 Thông tin nhà trường",
    description: "Quản lý thông tin cơ bản của nhà trường",
    order: 2,
    items: [
      {
        id: 4,
        name: "11.2.1 Thông tin nhà trường",
        description: "Cập nhật thông tin cơ bản nhà trường",
        icon: "school",
        href: "/he-thong/thong-tin-truong",
        order: 1,
        groupId: 2,
        isActive: true,
      },
      {
        id: 5,
        name: "11.2.2 Cấu hình năm học",
        description: "Thiết lập năm học và học kỳ",
        icon: "calendar",
        href: "/he-thong/nam-hoc",
        order: 2,
        groupId: 2,
        isActive: true,
      },
      {
        id: 6,
        name: "11.2.3 Cấu hình chữ ký",
        description: "Thiết lập chữ ký điện tử",
        icon: "signature",
        href: "/he-thong/chu-ky",
        order: 3,
        groupId: 2,
        isActive: true,
      },
      {
        id: 7,
        name: "11.2.4 Quản lý mẫu in",
        description: "Quản lý các mẫu in báo cáo",
        icon: "print",
        href: "/he-thong/mau-in",
        order: 4,
        groupId: 2,
        isActive: true,
      },
      {
        id: 8,
        name: "11.2.5 Đồng bộ dữ liệu CSDL Bộ",
        description: "Đồng bộ dữ liệu với cơ sở dữ liệu Bộ",
        icon: "sync",
        href: "/he-thong/dong-bo",
        order: 5,
        groupId: 2,
        isActive: true,
      },
    ],
  },
  {
    id: 3,
    name: "11.3 Trang chủ ban đọc",
    description: "Quản lý nội dung trang chủ",
    order: 3,
    items: [
      {
        id: 9,
        name: "11.3.1 Quản lý ảnh bìa (banner)",
        description: "Quản lý hình ảnh banner trang chủ",
        icon: "image",
        href: "/he-thong/banner",
        order: 1,
        groupId: 3,
        isActive: true,
      },
      {
        id: 10,
        name: "11.3.2 Quản lý bình luận",
        description: "Quản lý bình luận của người dùng",
        icon: "comment",
        href: "/he-thong/binh-luan",
        order: 2,
        groupId: 3,
        isActive: true,
      },
      {
        id: 11,
        name: "11.3.3 Liên kết website",
        description: "Quản lý các liên kết website",
        icon: "link",
        href: "/he-thong/lien-ket",
        order: 3,
        groupId: 3,
        isActive: true,
      },
    ],
  },
];

// Helper function to get all menu items in a flat array
export const getAllMenuItems = (groups: IMenuGroup[]): IMenuItem[] => {
  return groups.reduce(
    (acc, group) => [...acc, ...group.items],
    [] as IMenuItem[]
  );
};

// Helper function to get menu items by group
export const getMenuItemsByGroup = (
  groups: IMenuGroup[],
  groupId: number
): IMenuItem[] => {
  const group = groups.find((g) => g.id === groupId);
  return group ? group.items : [];
};
