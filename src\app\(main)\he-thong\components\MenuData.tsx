// Data transformation and helper functions for the menu system
import { IMenuItem, IMenuGroup } from "./types";
import { IMenuItemTree } from "@/models/menu.model";
import { DataConstant } from "@/constant";

/**
 * Transform IMenuItemTree to IMenuItem
 */
export const transformMenuItemTreeToMenuItem = (
  menuItem: IMenuItemTree,
  groupId: number
): IMenuItem => {
  return {
    id: menuItem.id,
    name: menuItem.name,
    description: undefined, // IMenuItemTree doesn't have description
    icon: menuItem.icon,
    href: menuItem.href,
    order: menuItem.order,
    groupId: groupId,
    isActive: menuItem.status === DataConstant.STATUS_TYPE.active,
  };
};

/**
 * Transform array of IMenuItemTree to IMenuGroup array
 * Groups items by their parent menu structure
 */
export const transformMenuTreeToGroups = (
  menuItems: IMenuItemTree[]
): IMenuGroup[] => {
  if (!menuItems || menuItems.length === 0) {
    return [];
  }

  const groups: IMenuGroup[] = [];

  menuItems.forEach((parentMenu) => {
    // Only process active menu items
    if (parentMenu.status !== DataConstant.STATUS_TYPE.active) {
      return;
    }

    if (parentMenu.children && parentMenu.children.length > 0) {
      // Filter active children
      const activeChildren = parentMenu.children.filter(
        (child) => child.status === DataConstant.STATUS_TYPE.active
      );

      if (activeChildren.length > 0) {
        // Create a group from parent menu with its active children as items
        const group: IMenuGroup = {
          id: parentMenu.id,
          name: parentMenu.name,
          description: undefined,
          order: parentMenu.order,
          items: activeChildren
            .map((child) =>
              transformMenuItemTreeToMenuItem(child, parentMenu.id)
            )
            .sort((a, b) => a.order - b.order),
        };
        groups.push(group);
      }
    } else {
      // If no children, create a group with the parent as a single item
      const group: IMenuGroup = {
        id: parentMenu.id,
        name: parentMenu.name,
        description: undefined,
        order: parentMenu.order,
        items: [transformMenuItemTreeToMenuItem(parentMenu, parentMenu.id)],
      };
      groups.push(group);
    }
  });

  // Sort groups by order
  return groups.sort((a, b) => a.order - b.order);
};

// Helper function to get all menu items in a flat array
export const getAllMenuItems = (groups: IMenuGroup[]): IMenuItem[] => {
  return groups.reduce(
    (acc, group) => [...acc, ...group.items],
    [] as IMenuItem[]
  );
};

// Helper function to get menu items by group
export const getMenuItemsByGroup = (
  groups: IMenuGroup[],
  groupId: number
): IMenuItem[] => {
  const group = groups.find((g) => g.id === groupId);
  return group ? group.items : [];
};
