import AppConfirmModal from "@/components/common/modal/AppConfirmModal";
import React, { memo } from "react";
import { useModalActionContext } from "../../ModalActionContext";
import useActionsData from "./hooks/useActionsData";

const DeleteModal = ({ url }: { url?: string }) => {
  const { isOpenDelete, closeModal, modalData } = useModalActionContext();
  const { handleDeleteData } = useActionsData();

  const handleConfirm = () => {
    handleDeleteData((modalData as any)?.id, url, closeModal);
  };

  return (
    <AppConfirmModal
      isOpen={isOpenDelete}
      modalTitleProps={{
        title: "Bạn chắc chắn muốn xóa bản ghi này?",
      }}
      modalContentProps={{}}
      onClose={closeModal}
      onConfirm={handleConfirm}
    />
  );
};

export default memo(DeleteModal);
