export interface ISubject {
  code: string;
  name: string;
  doetCode: string | null;
  divisionCode: string | null;
  schoolCode: string | null;
  groupUnitCode: string | null;
  order: number;
  isSystem: number;
  status: number;
  id: number;
  createdBy: string | null;
  updatedBy: string | null;
  createdAt: string | null;
  updatedAt: string | null;
}

export interface ISubjectParams {
  searchKey?: string;
}
