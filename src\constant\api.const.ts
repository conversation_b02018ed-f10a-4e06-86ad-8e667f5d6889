// Common
export const HEADER_DEFAULT = {
  Accept: "application/json",
  "Content-Type": "application/json",
};

export const TIMEOUT = 30000;

// HTTP Status
export const STT_OK = 200;
export const STT_CREATED = 201;
export const STT_BAD_REQUEST = 400;
export const STT_UNAUTHORIZED = 401;
export const STT_FORBIDDEN = 403;
export const STT_NOT_FOUND = 404;
export const STT_TIMEOUT = 408;
export const STT_INTERNAL_SERVER = 500;
export const STT_NOT_MODIFIED = 304;

export const CANCEL_MSG = "Request was cancelled";

// Error code
export const ERROR_CODE_OK = "1";

export const UPLOAD_IMG = "/api/v1/media/vlib/images";
export const UPLOAD_FILE = "/api/v1/media/vlib/files";

// app
export const GET_DOMAIN_INFO = "/get-domain-info";
export const GET_USER_INFO = "/v1/user/get-current-user-info";

// menu side bar
export const GET_MENU_SIDE_BAR = "/v1/school-menu-config/tree/{id}";

// system
export const GET_GRADE = "/v1/master-data/grade/list";

// Class
export const CLASS = "/v1/school-class";

// Subject
export const SUBJECT = "/v1/school-subject";
export const STATUS_SUBJECT = "/v1/school-subject/change-status/{id}/{status}";

// Device
export const DEVICE = "/v1/school-device-type";
export const STATUS_DEVICE =
  "/v1/school-device-type/change-status/{id}/{status}";

// Source
export const SOURCE = "/v1/school-budget-category";
export const STATUS_SOURCE =
  "/v1/school-budget-category/change-status/{id}/{status}";

// master data
export const GET_DON_VI = "/v1/master-data/school/list"; //api cho sở, phòng, trường

// login
export const LOGIN = "/v1/user/login-school";

// Room
export const ROOM = "/v1/equipment-room";
export const STATUS_ROOM = "/v1/equipment-room/change-status/{id}/{status}";
export const FUNCTIONAL_ROOM_TYPE =
  "/v1/master-data/functional-classroom-type/list";

// Teacher
export const TEACHER_COMBO = "/v1/teacher/combo";
