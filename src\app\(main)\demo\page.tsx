"use client";

import { AppFormTextField, TablePageLayout } from "@/components/common";
import {
  ExpandedCell,
  ExpandedHeaderCell,
} from "@/components/common/table/AppTable";
import { CheckIcon } from "@/components/icons";
import { CATEGORY_ID } from "@/constant/data.const";
import { ISubject } from "@/models/subject.model";
import { ITree } from "@/models/types";
import { Tab, Tabs } from "@mui/material";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import React from "react";

const CATEGORY_TYPE = [
  { id: CATEGORY_ID.paper, label: "Sách giấy" },
  { id: CATEGORY_ID.ebook, label: "Sách điện tử" },
  { id: CATEGORY_ID.audioBook, label: "Sách nói" },
  { id: CATEGORY_ID.album, label: "Album Ảnh" },
  { id: CATEGORY_ID.video, label: "Video" },
  { id: CATEGORY_ID.electure, label: "<PERSON>ài giảng điện tử" },
  { id: CATEGORY_ID.skill, label: "<PERSON><PERSON> năng sống" },
  { id: CATEGORY_ID.emagazine, label: "Báo tạp chí" },
];

const page = () => {
  const columns: ColumnDef<ISubject>[] = [
    {
      id: "expanded",
      size: 0,
      minSize: 0,
      header: ({ table }) => <ExpandedHeaderCell table={table} />,
      meta: {
        align: "center",
        cellSx: {
          width: "1px !important",
        },
      },
      cell: ({ row }) => <ExpandedCell row={row} />,
    },
    {
      id: "code",
      header: "Mã",
      accessorKey: "code",
      size: 80,
    },
    {
      id: "name",
      header: "Tên môn học",
      accessorKey: "name",
      size: 200,
    },
    {
      id: "order",
      accessorKey: "order",
      header: "Thứ tự",
      size: 80,
      meta: {
        align: "center",
      },
    },
    {
      id: "isSystem",
      header: "Môn hệ thống",
      size: 60,
      meta: {
        align: "center",
      },
      cell: ({ row }) =>
        Boolean(row.original.isSystem) && (
          <CheckIcon
            sx={{
              fontSize: 24,
              color: "primary.main",
            }}
          />
        ),
    },
  ];

  const LIST = [
    {
      id: 1,
      label: "Quy trình",
    },
    {
      id: 2,
      label: "Danh sách thiết bị",
    },
    {
      id: 3,
      label: "Kiểm kê",
    },
    {
      id: 4,
      label: "Theo dõi hỏng/mất",
    },
  ];

  return (
    <>
      <Tabs
        value={1}
        TabIndicatorProps={{
          sx: {
            top: 0,
            bottom: "auto",
          },
        }}
      >
        {LIST.map((item) => (
          <Tab value={item.id} label={item.label} key={item.id} />
        ))}
      </Tabs>
      <TablePageLayout<ISubject>
        fetchAll
        visibleCol={[
          {
            id: "code",
            name: "Mã",
          },
          {
            id: "name",
            name: "Tên môn học",
          },
          {
            id: "order",
            name: "Thứ tự",
          },
          {
            id: "isSystem",
            name: "Môn hệ thống",
          },
        ]}
        customActions={
          <>
            <button>xóa</button>
          </>
        }
        apiUrl={"/v1/school-category"}
        formatData={convertCategoryData}
        tableProps={{
          columns,
          onRowSelectionChange: (value) => {
            // value là các item đang được selected
          },
          paginationData: undefined,
          options: {
            getSubRows: (row) => (row as any).children,
          },
        }}
        filterConfig={[
          {
            key: "categoryTypeId",
            type: "select",
            label: "Loại thư mục",
            size: 2.4,
            options: CATEGORY_TYPE,
            value: CATEGORY_TYPE[0].id,
            fieldProps: {
              disableClearable: true,
            },
          },

          {
            key: "created",
            type: "date",
            label: "Ngày",
            size: 4,
            isAdvanced: true,
          },
          {
            key: "dateRange",
            type: "dateRange",
            label: "Ngày range",
            size: 8,
            isAdvanced: true,
            keyDateRange: ["fromDate", "toDate"],
            value: [dayjs("2025-04-30").toDate(), dayjs("2025-05-06").toDate()],
          },

          {
            key: "searchKey",
            type: "text",
            label: "Tìm kiếm",
            size: 2.4,
          },
        ]}
        actions={["create", "update", "delete", "check"]}
        formConfig={{
          modalProps: {
            fullScreen: true,
          },
          deleteUrl: "/v1/school-subject",
          detailUrl: "/v1/school-category/find-exclude-childs",
          createUrl: "/v1/school-subject",
          updateUrl: "/v1/school-subject",
          createFields: [
            {
              key: "code",
              type: "text",
              label: "Mã môn học",
              size: 12,
              rules: {
                required: "Mã môn học không được để trống",
              },
            },
            {
              key: "name",
              type: "text",
              label: "Tên môn học",
              size: 12,
              rules: {
                required: "Tên môn học không được để trống",
              },
            },
            {
              key: "order",
              type: "number",
              label: "Thứ tự",
              size: 12,
              defaultValue: 1,
              textFieldNumberProps: {
                min: 3,
                max: 5,
              },
            },
            {
              key: "groupUnitCode",
              type: "text",
              label: "Mô tả",
              size: 12,
              textFieldProps: {
                multiline: true,
                rows: 4,
              },
            },
            {
              key: "isSystem",
              type: "select",
              label: "Môn hệ thống",
              size: 12,
              selectConfig: {
                options: [
                  { id: 1, label: "Có" },
                  { id: 0, label: "Không" },
                ],
              },
            },
            {
              key: "status",
              type: "toggle",
              label: "Trạng thái hiển thị",
              size: 12,
            },
            {
              key: "schoolCode",
              type: "editor",
              label: "Mô tả",
              size: 12,
              rules: {
                maxLength: {
                  value: 10,
                  message: "không vượt quá 10 ký tự",
                },
              },
            },
            {
              key: "createdAt",
              type: "date",
              label: "Ngày tạo",
              size: 12,
              dataPickerProps: {},
            },
            {
              key: "divisionCode",
              type: "custom",
              component: (control) => (
                <AppFormTextField
                  control={control}
                  label="hiehe"
                  name="divisionCode"
                />
              ),
            },
          ],
          updateFields: [
            {
              key: "code",
              type: "text",
              label: "Mã môn học",
              size: 12,
              rules: {
                required: "Mã môn học không được để trống",
              },
            },
            {
              key: "name",
              type: "text",
              label: "Tên môn học",
              size: 12,
              rules: {
                required: "Tên môn học không được để trống",
              },
            },
            {
              key: "order",
              type: "number",
              label: "Thứ tự",
              size: 12,
              defaultValue: 1,
              textFieldNumberProps: {
                min: 3,
                max: 5,
              },
            },
            {
              key: "groupUnitCode",
              type: "text",
              label: "Mô tả",
              size: 12,
              textFieldProps: {
                multiline: true,
                rows: 4,
              },
            },
            {
              key: "isSystem",
              type: "select",
              label: "Môn hệ thống",
              size: 12,
              selectConfig: {
                options: [
                  { id: 1, label: "Có" },
                  { id: 0, label: "Không" },
                ],
              },
            },
            {
              key: "status",
              type: "toggle",
              label: "Trạng thái hiển thị",
              size: 12,
            },
            {
              key: "schoolCode",
              type: "editor",
              label: "Mô tả",
              size: 12,
              rules: {
                maxLength: {
                  value: 10,
                  message: "không vượt quá 10 ký tự",
                },
              },
            },
            {
              key: "createdAt",
              type: "date",
              label: "Ngày tạo",
              size: 12,
              dataPickerProps: {},
            },
            {
              key: "divisionCode",
              type: "custom",
              size: 6,
              component: (control) => (
                <AppFormTextField
                  control={control}
                  label="custom field"
                  name="divisionCode"
                />
              ),
            },
          ],
        }}
      />
    </>
  );
};

export default page;

const convertCategoryData = (inputData: any[]): ITree[] => {
  const result: ITree[] = [];
  const map: Record<string | number, ITree> = {};

  // Kiểm tra nếu dữ liệu đầu vào không hợp lệ
  if (!Array.isArray(inputData) || inputData.length === 0) {
    return result; // Trả về mảng rỗng nếu dữ liệu không hợp lệ
  }

  // Tạo các phần tử và lưu vào map
  inputData.forEach((item) => {
    // Kiểm tra dữ liệu item hợp lệ trước khi xử lý
    if (item && item.id && item.parentId !== undefined) {
      const { id, parentId: itemParentId, ...otherProps } = item;

      const treeItem: ITree = {
        id,
        parentId: itemParentId,
        ...otherProps,
        children: [],
      };

      // Lưu phần tử vào map theo id
      map[id] = treeItem;
    }
  });

  // Xây dựng cây
  inputData.forEach((item) => {
    // Kiểm tra item hợp lệ
    if (item && item.id && item.parentId !== undefined) {
      const { id, parentId: itemParentId } = item;

      // Nếu không tìm thấy parentId trong mảng, coi phần tử này là gốc
      if (!map[itemParentId]) {
        result.push(map[id]);
      } else {
        // Nếu tìm thấy cha trong map, thêm phần tử vào children của cha
        map[itemParentId]?.children?.push(map[id]);
      }
    }
  });

  return result;
};
