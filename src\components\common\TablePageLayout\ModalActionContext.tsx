import http from "@/api";
import { ApiConstant } from "@/constant";
import { DataResponseModel } from "@/models/response.model";
import { toggleAppProgress } from "@/utils/common.utils";
import React, { createContext, useContext, useState, ReactNode } from "react";
import { toast } from "sonner";

type ModalActionContextType<T = any> = {
  openCreateModal: () => void;
  openEditModal: (data?: T) => void;
  openDeleteModal: (data?: T) => void;
  closeModal: () => void;
  modalData: T | null;
  isOpenCreate: boolean;
  isOpenEdit: boolean;
  isOpenDelete: boolean;
};

const ModalActionContext = createContext<ModalActionContextType | undefined>(
  undefined
);

export const useModalActionContext = <T,>() => {
  const context = useContext(ModalActionContext);
  if (!context) {
    throw new Error(
      "useModalActionContext must be used within a ModalActionProvider"
    );
  }
  return context as ModalActionContextType<T>;
};

export type ModalActionProviderProps = {
  children: ReactNode;
  detailUrl?: string;
};
export const ModalActionProvider: React.FC<ModalActionProviderProps> = ({
  children,
  detailUrl,
}) => {
  const [isOpenCreate, setIsOpenCreate] = useState(false);
  const [isOpenEdit, setIsOpenEdit] = useState(false);
  const [isOpenDelete, setIsOpenDelete] = useState(false);
  const [modalData, setModalData] = useState<any>(null);

  const closeModal = () => {
    setIsOpenCreate(false);
    setIsOpenEdit(false);
    setIsOpenDelete(false);
    setModalData(null);
  };

  const openCreateModal = () => {
    closeModal(); // ensure only one modal open
    setModalData(null);
    setIsOpenCreate(true);
  };

  const openEditModal = (data?: any) => {
    closeModal();
    setIsOpenEdit(true);
    getDataDetail(setModalData, data?.id, detailUrl);
  };

  const openDeleteModal = (data?: any) => {
    closeModal();
    setModalData(data ?? null);
    setIsOpenDelete(true);
  };

  return (
    <ModalActionContext.Provider
      value={{
        openCreateModal,
        openEditModal,
        openDeleteModal,
        closeModal,
        modalData,
        isOpenCreate,
        isOpenEdit,
        isOpenDelete,
      }}
    >
      {children}
    </ModalActionContext.Provider>
  );
};
const getDataDetail = async <T = unknown,>(
  onSuccess: (data: T) => void,
  id?: string | number,
  url?: string
) => {
  try {
    toggleAppProgress(true);

    if (!id) throw new Error("Id không xác định");
    if (!url) throw new Error("Url không xác định");

    const res: DataResponseModel<T> = await http.get(url + "/" + id);

    if (res.code === ApiConstant.ERROR_CODE_OK) {
      onSuccess(res.data);
    } else {
      throw res;
    }
  } catch (error) {
    console.error("Lỗi khi lấy chi tiết dữ liệu:", error);

    toast.error("Thất bại!", {
      description: "Lỗi khi lấy chi tiết dữ liệu",
    });
  } finally {
    toggleAppProgress(false);
  }
};
