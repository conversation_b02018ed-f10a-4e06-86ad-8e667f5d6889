"use client";

import { FilterIcon, CloseIcon, ClearFilterIcon } from "@/components/icons";
import {
  Button,
  Grid,
  IconButton,
  Popover,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { memo, useMemo, useState } from "react";
import { useTableContext } from "../../TableContext";
import { SPACING_COLUMN_FILTER, SPACING_ROW_FILTER } from ".";
import ListFilter from "./ListFilter";
import IconButtonCustom from "./IconButtonCustom";

const AdvancedFilter = <T,>() => {
  const { filter, handleClearFilter } = useTableContext<T>();

  const filterShow = useMemo(
    () => filter?.filter((item) => item.isAdvanced && item.type) || [],
    [filter]
  );

  const [anchor, setAnchor] = useState<HTMLButtonElement | null>(null);

  return (
    <>
      <Tooltip arrow title="Bộ lọc nâng cao">
        <IconButtonCustom
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) =>
            setAnchor(e.currentTarget)
          }
        >
          <FilterIcon
            sx={{
              fontSize: 20,
            }}
          />
        </IconButtonCustom>
      </Tooltip>
      <Popover
        open={Boolean(anchor)}
        anchorEl={anchor}
        onClose={() => setAnchor(null)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        slotProps={{
          paper: {
            sx: {
              p: 2,
              width: 800,
              border: "1px solid",
              borderColor: "grey.300",
              overflow: "visible",
            },
          },
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="subtitle2" fontWeight={600}>
            Bộ lọc nâng cao
          </Typography>
          <IconButton size="small" onClick={() => setAnchor(null)}>
            <CloseIcon />
          </IconButton>
        </Stack>
        <Grid
          container
          columnSpacing={SPACING_COLUMN_FILTER}
          rowSpacing={SPACING_ROW_FILTER}
          pt={3}
          pb={2}
        >
          <ListFilter filter={filterShow} />
        </Grid>
        <Stack
          sx={{
            alignItems: "flex-end",
          }}
        >
          <Button
            variant="outlined"
            sx={{
              width: "fit-content",
            }}
            startIcon={<ClearFilterIcon />}
            onClick={() => {
              handleClearFilter();
              setAnchor(null);
            }}
          >
            Xóa bộ lọc
          </Button>
        </Stack>
      </Popover>
    </>
  );
};

export default memo(AdvancedFilter);
