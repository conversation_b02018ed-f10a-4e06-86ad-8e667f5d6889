"use client";

import React, { memo } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  CardActionArea,
} from "@mui/material";
import { ArrowIcon, SchoolIcon } from "@/components/icons";
import { AppLink } from "@/components/common";
import { IMenuItem } from "./types";
import { CardVariant } from "./enums";

type MenuCardProps = {
  item: IMenuItem;
  variant?: CardVariant;
  onClick?: (item: IMenuItem) => void;
};

const MenuCard: React.FC<MenuCardProps> = ({
  item,
  variant = CardVariant.CARD,
  onClick,
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  // Placeholder icon component - can be replaced with actual icons later
  const IconComponent = SchoolIcon; // Default icon, can be mapped based on item.icon

  if (variant === CardVariant.LIST) {
    return null;
  }

  return (
    <Card
      sx={{
        height: "100%",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        "&:hover": {
          boxShadow: "0 4px 16px rgba(0,0,0,0.15)",
          transform: "translateY(-2px)",
        },
        transition: "all 0.3s ease-in-out",
      }}
    >
      <CardActionArea
        component={AppLink}
        href={item.href}
        onClick={handleClick}
        sx={{ height: "100%" }}
      >
        <CardContent
          sx={{
            p: 3,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <Box
            sx={{
              width: 64,
              height: 64,
              borderRadius: "50%",
              backgroundColor: "primary.light",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
            }}
          >
            <IconComponent
              sx={{
                fontSize: 32,
                color: "primary.main",
              }}
            />
          </Box>

          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: "text.primary",
              mb: 1,
              fontSize: "1rem",
              lineHeight: 1.3,
            }}
          >
            {item.name}
          </Typography>

          <Box sx={{ mt: 2, display: "flex", alignItems: "center", gap: 1 }}>
            {item.isActive && (
              <Typography
                variant="body2"
                sx={{
                  fontSize: "0.75rem",
                }}
              >
                Xem
              </Typography>
            )}
            <ArrowIcon
              sx={{
                fontSize: 16,
                transform: "rotate(180deg)",
              }}
            />
          </Box>
        </CardContent>
      </CardActionArea>
    </Card>
  );
};

export default memo(MenuCard);
